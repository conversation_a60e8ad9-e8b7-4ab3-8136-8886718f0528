# IDM Activator GUI - واجهة تفعيل IDM

## نظرة عامة / Overview

هذا التطبيق هو واجهة رسومية مطورة بـ C# Windows Forms لتفعيل وإدارة Internet Download Manager (IDM). التطبيق يوفر واجهة سهلة الاستخدام باللغة العربية والإنجليزية.

This application is a C# Windows Forms GUI for activating and managing Internet Download Manager (IDM). The application provides an easy-to-use interface in both Arabic and English.

## المطور / Developer

**تطوير: المهندس محمد عيسى**  
**Developer: Engineer Mohamed <PERSON>**

📱 **الهاتف / Phone:** 01009046911  
🎥 **القناة / Channel:** قناة دارك سايبر اكس على يوتيوب  
🌐 **الموقع / Website:** DarkCyberX

## الميزات / Features

### العربية
- **تفعيل IDM**: تفعيل Internet Download Manager (قد لا يعمل مع الإصدارات الحديثة)
- **تجميد فترة التجربة**: تجميد فترة التجربة لمدة 30 يوم للاستخدام الدائم
- **إعادة تعيين التفعيل**: إعادة تعيين تفعيل IDM والعودة لحالة التجربة
- **تحميل IDM**: تحميل أحدث إصدار من IDM من الموقع الرسمي مباشرة
- **إزالة IDM نهائياً**: حذف IDM من الجذور بشكل كامل مع جميع الملفات والإعدادات
- **واجهة عربية**: دعم كامل للغة العربية مع تخطيط من اليمين لليسار
- **مراقبة العمليات**: عرض تفصيلي لجميع العمليات المنفذة
- **فحص الصلاحيات**: التحقق من صلاحيات المدير تلقائياً
- **إزالة إضافات المتصفح**: حذف إضافات IDM من Chrome وFirefox تلقائياً
- **أيقونة مخصصة**: أيقونة IDM مخصصة للتطبيق

### English
- **IDM Activation**: Activate Internet Download Manager (may not work with newer versions)
- **Freeze Trial**: Freeze the 30-day trial period for permanent use
- **Reset Activation**: Reset IDM activation and return to trial state
- **Download IDM**: Download the latest version of IDM directly from the official website
- **Complete IDM Removal**: Completely remove IDM from roots with all files and settings
- **Arabic Interface**: Full Arabic language support with right-to-left layout
- **Process Monitoring**: Detailed display of all executed operations
- **Permission Check**: Automatic administrator privileges verification
- **Browser Extensions Removal**: Automatically remove IDM extensions from Chrome and Firefox
- **Custom Icon**: Custom IDM icon for the application

## متطلبات النظام / System Requirements

- Windows 7/8/8.1/10/11
- .NET 6.0 أو أحدث / .NET 6.0 or later
- Internet Download Manager مثبت / Internet Download Manager installed
- صلاحيات المدير (مُنصح بها) / Administrator privileges (recommended)

## طريقة الاستخدام / How to Use

### العربية
1. تأكد من تثبيت IDM على النظام
2. شغل التطبيق كمدير (مُنصح)
3. اختر العملية المطلوبة:
   - **تفعيل IDM**: للتفعيل الكامل (قد لا يعمل مع الإصدارات الحديثة)
   - **تجميد فترة التجربة**: الخيار المُنصح للاستخدام الدائم
   - **إعادة تعيين**: لإعادة تعيين التفعيل
4. انتظر انتهاء العملية
5. أعد تشغيل IDM

### English
1. Ensure IDM is installed on the system
2. Run the application as administrator (recommended)
3. Choose the required operation:
   - **Activate IDM**: For full activation (may not work with newer versions)
   - **Freeze Trial**: Recommended option for permanent use
   - **Reset**: To reset activation
4. Wait for the operation to complete
5. Restart IDM

## ملاحظات مهمة / Important Notes

### العربية
- **تحذير**: وظيفة التفعيل قد لا تعمل مع الإصدارات الحديثة من IDM
- **الخيار المُنصح**: استخدم "تجميد فترة التجربة" للحصول على أفضل النتائج
- **الصلاحيات**: يُنصح بتشغيل التطبيق كمدير للحصول على أفضل النتائج
- **الأمان**: التطبيق يقوم بإيقاف عمليات IDM قبل التعديل لضمان الأمان

### English
- **Warning**: Activation function may not work with newer IDM versions
- **Recommended**: Use "Freeze Trial" for best results
- **Permissions**: Running as administrator is recommended for best results
- **Safety**: The application stops IDM processes before modification for safety

## البناء والتطوير / Build and Development

```bash
# استنساخ المشروع / Clone the project
git clone [repository-url]

# الانتقال للمجلد / Navigate to directory
cd IDMActivatorGUI

# بناء المشروع / Build the project
dotnet build

# تشغيل التطبيق / Run the application
dotnet run
```

## إخلاء المسؤولية / Disclaimer

هذا التطبيق مخصص للأغراض التعليمية فقط. المطور غير مسؤول عن أي استخدام غير قانوني للتطبيق. يُرجى احترام حقوق الطبع والنشر وشراء النسخة الأصلية من IDM.

This application is for educational purposes only. The developer is not responsible for any illegal use of the application. Please respect copyrights and purchase the original version of IDM.

## الترخيص / License

هذا المشروع مفتوح المصدر ومتاح تحت رخصة MIT.

This project is open source and available under the MIT License.

---

**تطوير بواسطة المهندس محمد عيسى - قناة دارك سايبر اكس**  
**Developed by Engineer Mohamed Issa - DarkCyberX Channel**
