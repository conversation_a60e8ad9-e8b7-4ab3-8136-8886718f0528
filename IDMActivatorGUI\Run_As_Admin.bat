@echo off
title IDM Activator - DarkCyberX
echo.
echo ========================================
echo   IDM Activator GUI - DarkCyberX
echo ========================================
echo   تطوير: المهندس محمد عيسى
echo   Developer: Engineer <PERSON>
echo   Phone: 01009046911
echo   Channel: قناة دارك سايبر اكس على يوتيوب
echo   Website: DarkCyberX
echo ========================================
echo.
echo تشغيل التطبيق كمدير...
echo Running application as administrator...
echo.

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo تم تشغيل الملف بصلاحيات المدير
    echo Running with administrator privileges
    echo.
    goto :run_app
) else (
    echo طلب صلاحيات المدير...
    echo Requesting administrator privileges...
    echo.
    powershell -Command "Start-Process '%~dp0bin\Release\net6.0-windows\win-x64\publish\IDMActivatorGUI.exe' -Verb RunAs"
    goto :end
)

:run_app
start "" "%~dp0bin\Release\net6.0-windows\win-x64\publish\IDMActivatorGUI.exe"

:end
echo.
echo شكراً لاستخدام IDM Activator
echo Thank you for using IDM Activator
echo.
pause
