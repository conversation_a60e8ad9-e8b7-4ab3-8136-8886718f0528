# ===============================================
# اختبار تطبيق IDM Activator GUI
# Test IDM Activator GUI Application
# ===============================================

Write-Host "===============================================" -ForegroundColor Cyan
Write-Host "    اختبار تطبيق IDM Activator GUI" -ForegroundColor Yellow
Write-Host "    Testing IDM Activator GUI Application" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan

# Test 1: Check if application executable exists
Write-Host "`n1️⃣ فحص وجود ملف التطبيق..." -ForegroundColor Green
Write-Host "1️⃣ Checking application executable..." -ForegroundColor Green

$appPath = ".\bin\Debug\net6.0-windows\IDMActivatorGUI.exe"
if (Test-Path $appPath) {
    Write-Host "✅ تم العثور على ملف التطبيق: $appPath" -ForegroundColor Green
    Write-Host "✅ Application executable found: $appPath" -ForegroundColor Green
    
    # Get file info
    $fileInfo = Get-Item $appPath
    Write-Host "📁 حجم الملف: $($fileInfo.Length) bytes" -ForegroundColor Cyan
    Write-Host "📁 File size: $($fileInfo.Length) bytes" -ForegroundColor Cyan
    Write-Host "📅 تاريخ التعديل: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
    Write-Host "📅 Last modified: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "❌ لم يتم العثور على ملف التطبيق" -ForegroundColor Red
    Write-Host "❌ Application executable not found" -ForegroundColor Red
    exit 1
}

# Test 2: Check if IDM is installed
Write-Host "`n2️⃣ فحص تثبيت IDM..." -ForegroundColor Green
Write-Host "2️⃣ Checking IDM installation..." -ForegroundColor Green

$idmPaths = @(
    "${env:ProgramFiles}\Internet Download Manager\IDMan.exe",
    "${env:ProgramFiles(x86)}\Internet Download Manager\IDMan.exe"
)

$idmFound = $false
foreach ($path in $idmPaths) {
    if (Test-Path $path) {
        Write-Host "✅ تم العثور على IDM في: $path" -ForegroundColor Green
        Write-Host "✅ IDM found at: $path" -ForegroundColor Green
        $idmFound = $true
        
        # Get IDM version
        try {
            $versionInfo = (Get-Item $path).VersionInfo
            Write-Host "📋 إصدار IDM: $($versionInfo.ProductVersion)" -ForegroundColor Cyan
            Write-Host "📋 IDM Version: $($versionInfo.ProductVersion)" -ForegroundColor Cyan
        } catch {
            Write-Host "⚠️ لا يمكن قراءة إصدار IDM" -ForegroundColor Yellow
            Write-Host "⚠️ Cannot read IDM version" -ForegroundColor Yellow
        }
        break
    }
}

if (-not $idmFound) {
    Write-Host "❌ لم يتم العثور على IDM مثبت" -ForegroundColor Red
    Write-Host "❌ IDM not found installed" -ForegroundColor Red
}

# Test 3: Check internet connectivity
Write-Host "`n3️⃣ فحص الاتصال بالإنترنت..." -ForegroundColor Green
Write-Host "3️⃣ Checking internet connectivity..." -ForegroundColor Green

try {
    $ping = Test-Connection -ComputerName "internetdownloadmanager.com" -Count 1 -Quiet
    if ($ping) {
        Write-Host "✅ الاتصال بـ internetdownloadmanager.com ناجح" -ForegroundColor Green
        Write-Host "✅ Connection to internetdownloadmanager.com successful" -ForegroundColor Green
    } else {
        Write-Host "❌ فشل الاتصال بـ internetdownloadmanager.com" -ForegroundColor Red
        Write-Host "❌ Failed to connect to internetdownloadmanager.com" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في فحص الاتصال: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Error checking connection: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Check registry access
Write-Host "`n4️⃣ فحص صلاحيات الريجستري..." -ForegroundColor Green
Write-Host "4️⃣ Checking registry access..." -ForegroundColor Green

try {
    # Test HKCU access
    $hkcuTest = Get-ItemProperty -Path "HKCU:\Software" -Name "(default)" -ErrorAction SilentlyContinue
    Write-Host "✅ صلاحية قراءة HKCU متاحة" -ForegroundColor Green
    Write-Host "✅ HKCU read access available" -ForegroundColor Green
    
    # Test HKLM access (requires admin)
    try {
        $hklmTest = Get-ItemProperty -Path "HKLM:\SOFTWARE" -Name "(default)" -ErrorAction SilentlyContinue
        Write-Host "✅ صلاحية قراءة HKLM متاحة" -ForegroundColor Green
        Write-Host "✅ HKLM read access available" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ صلاحية HKLM محدودة - قد تحتاج لتشغيل كمدير" -ForegroundColor Yellow
        Write-Host "⚠️ HKLM access limited - may need to run as administrator" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ خطأ في فحص الريجستري: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Error checking registry: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Check if running as administrator
Write-Host "`n5️⃣ فحص صلاحيات المدير..." -ForegroundColor Green
Write-Host "5️⃣ Checking administrator privileges..." -ForegroundColor Green

$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
if ($isAdmin) {
    Write-Host "✅ يعمل بصلاحيات المدير" -ForegroundColor Green
    Write-Host "✅ Running with administrator privileges" -ForegroundColor Green
} else {
    Write-Host "⚠️ لا يعمل بصلاحيات المدير - قد تفشل بعض العمليات" -ForegroundColor Yellow
    Write-Host "⚠️ Not running with administrator privileges - some operations may fail" -ForegroundColor Yellow
}

# Test 6: Start application and test basic functionality
Write-Host "`n6️⃣ اختبار تشغيل التطبيق..." -ForegroundColor Green
Write-Host "6️⃣ Testing application startup..." -ForegroundColor Green

try {
    # Start the application
    $process = Start-Process -FilePath $appPath -PassThru -WindowStyle Normal
    
    if ($process) {
        Write-Host "✅ تم تشغيل التطبيق بنجاح - Process ID: $($process.Id)" -ForegroundColor Green
        Write-Host "✅ Application started successfully - Process ID: $($process.Id)" -ForegroundColor Green
        
        # Wait a moment for the application to load
        Start-Sleep -Seconds 3
        
        # Check if process is still running
        if (-not $process.HasExited) {
            Write-Host "✅ التطبيق يعمل بشكل طبيعي" -ForegroundColor Green
            Write-Host "✅ Application running normally" -ForegroundColor Green
            
            # Give user time to interact with the application
            Write-Host "`n🎯 التطبيق جاهز للاختبار!" -ForegroundColor Magenta
            Write-Host "🎯 Application ready for testing!" -ForegroundColor Magenta
            Write-Host "📋 يمكنك الآن اختبار الوظائف التالية:" -ForegroundColor Cyan
            Write-Host "📋 You can now test the following functions:" -ForegroundColor Cyan
            Write-Host "   • تفعيل IDM (Activate IDM)" -ForegroundColor White
            Write-Host "   • تجميد فترة التجربة (Freeze Trial)" -ForegroundColor White
            Write-Host "   • إعادة تعيين IDM (Reset IDM)" -ForegroundColor White
            Write-Host "   • إلغاء التثبيت (Uninstall)" -ForegroundColor White
            
            Write-Host "`n⏰ سيتم إغلاق التطبيق تلقائياً بعد 30 ثانية..." -ForegroundColor Yellow
            Write-Host "⏰ Application will close automatically after 30 seconds..." -ForegroundColor Yellow
            
            # Wait for 30 seconds
            Start-Sleep -Seconds 30
            
            # Close the application
            if (-not $process.HasExited) {
                $process.CloseMainWindow()
                Start-Sleep -Seconds 2
                if (-not $process.HasExited) {
                    $process.Kill()
                }
                Write-Host "✅ تم إغلاق التطبيق" -ForegroundColor Green
                Write-Host "✅ Application closed" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ التطبيق توقف بشكل غير متوقع" -ForegroundColor Red
            Write-Host "❌ Application exited unexpectedly" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ فشل في تشغيل التطبيق" -ForegroundColor Red
        Write-Host "❌ Failed to start application" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Error starting application: $($_.Exception.Message)" -ForegroundColor Red
}

# Test Summary
Write-Host "`n===============================================" -ForegroundColor Cyan
Write-Host "    ملخص نتائج الاختبار" -ForegroundColor Yellow
Write-Host "    Test Results Summary" -ForegroundColor Yellow
Write-Host "===============================================" -ForegroundColor Cyan

Write-Host "✅ ملف التطبيق: موجود" -ForegroundColor Green
Write-Host "✅ Application file: Found" -ForegroundColor Green

if ($idmFound) {
    Write-Host "✅ تثبيت IDM: موجود" -ForegroundColor Green
    Write-Host "✅ IDM installation: Found" -ForegroundColor Green
} else {
    Write-Host "❌ تثبيت IDM: غير موجود" -ForegroundColor Red
    Write-Host "❌ IDM installation: Not found" -ForegroundColor Red
}

if ($isAdmin) {
    Write-Host "✅ صلاحيات المدير: متاحة" -ForegroundColor Green
    Write-Host "✅ Administrator privileges: Available" -ForegroundColor Green
} else {
    Write-Host "⚠️ صلاحيات المدير: غير متاحة" -ForegroundColor Yellow
    Write-Host "⚠️ Administrator privileges: Not available" -ForegroundColor Yellow
}

Write-Host "`n🎉 انتهى اختبار التطبيق!" -ForegroundColor Magenta
Write-Host "🎉 Application testing completed!" -ForegroundColor Magenta
