{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\IDM DarkCyberX\\IDMActivatorGUI\\IDMActivatorGUI.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\IDM DarkCyberX\\IDMActivatorGUI\\IDMActivatorGUI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\IDM DarkCyberX\\IDMActivatorGUI\\IDMActivatorGUI.csproj", "projectName": "IDMActivatorGUI", "projectPath": "C:\\Users\\<USER>\\Desktop\\IDM DarkCyberX\\IDMActivatorGUI\\IDMActivatorGUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\IDM DarkCyberX\\IDMActivatorGUI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}