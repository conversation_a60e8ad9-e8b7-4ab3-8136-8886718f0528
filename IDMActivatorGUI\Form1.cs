using System.Diagnostics;
using System.Security.Principal;
using Microsoft.Win32;
using System.Net.NetworkInformation;
using System.Net;
using System.Security.AccessControl;
using System.Runtime.InteropServices;

namespace IDMActivatorGUI;

public partial class Form1 : Form
{
    private bool isProcessing = false;

    // Advanced system variables (like original script)
    private string _sid = "";
    private bool HKCUsync = false;
    private string CLSID = "";
    private string CLSID2 = "";
    private string HKLM = "";
    private string IDMan = "";
    private string arch = "";
    private List<string> finalValues = new List<string>();

    // P/Invoke declarations for advanced registry operations
    [DllImport("ntdll.dll", SetLastError = true)]
    private static extern int RtlAdjustPrivilege(int privilege, bool enable, bool currentThread, out bool previousValue);

    [DllImport("advapi32.dll", SetLastError = true)]
    private static extern bool OpenProcessToken(IntPtr ProcessHandle, uint DesiredAccess, out IntPtr TokenHandle);

    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern IntPtr GetCurrentProcess();

    private const int SE_BACKUP_PRIVILEGE = 17;
    private const int SE_RESTORE_PRIVILEGE = 18;
    private const int SE_TAKE_OWNERSHIP_PRIVILEGE = 9;

    public Form1()
    {
        InitializeComponent();
        LoadIcon();
        CheckAdminRights();

        // Initialize advanced system variables (like original script)
        InitializeAdvancedSystem();
    }

    private void LoadIcon()
    {
        try
        {
            // Try to load PNG file and convert to icon
            string pngPath = "internet-download-manager-icon.png";
            if (File.Exists(pngPath))
            {
                using (var bitmap = new Bitmap(pngPath))
                {
                    // Resize to standard icon size if needed
                    var resizedBitmap = new Bitmap(bitmap, new Size(32, 32));
                    this.Icon = Icon.FromHandle(resizedBitmap.GetHicon());
                }
                AppendOutput("تم تحميل أيقونة التطبيق بنجاح");
                AppendOutput("Application icon loaded successfully");
            }
            else
            {
                AppendOutput("لم يتم العثور على ملف الأيقونة");
                AppendOutput("Icon file not found");
            }
        }
        catch (Exception ex)
        {
            // Log error but continue without icon
            AppendOutput($"تعذر تحميل الأيقونة: {ex.Message}");
            AppendOutput($"Could not load icon: {ex.Message}");
        }
    }

    private void CheckAdminRights()
    {
        bool isAdmin = new WindowsPrincipal(WindowsIdentity.GetCurrent())
            .IsInRole(WindowsBuiltInRole.Administrator);

        if (!isAdmin)
        {
            AppendOutput("تحذير: يُنصح بتشغيل البرنامج كمدير للحصول على أفضل النتائج");
            AppendOutput("Warning: It's recommended to run as administrator for best results");
        }
        else
        {
            AppendOutput("تم تشغيل البرنامج بصلاحيات المدير");
            AppendOutput("Running with administrator privileges");
        }
    }

    private void AppendOutput(string text)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<string>(AppendOutput), text);
            return;
        }

        txtOutput.AppendText($"{DateTime.Now:HH:mm:ss} - {text}\r\n");
        txtOutput.ScrollToCaret();
    }

    private void SetStatus(string status)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<string>(SetStatus), status);
            return;
        }

        lblStatus.Text = status;
    }

    private void SetProcessing(bool processing)
    {
        if (InvokeRequired)
        {
            Invoke(new Action<bool>(SetProcessing), processing);
            return;
        }

        isProcessing = processing;
        btnActivate.Enabled = !processing;
        btnFreezeTrial.Enabled = !processing;
        btnReset.Enabled = !processing;
        btnDownloadIDM.Enabled = !processing;
        btnUninstallIDM.Enabled = !processing;
        progressBar.Visible = processing;

        if (processing)
        {
            progressBar.Style = ProgressBarStyle.Marquee;
        }
    }

    private async void btnActivate_Click(object sender, EventArgs e)
    {
        if (isProcessing) return;

        SetProcessing(true);
        SetStatus("جاري تفعيل IDM...");
        AppendOutput("بدء عملية تفعيل IDM");
        AppendOutput("Starting IDM activation process");

        await Task.Run(() => ActivateIDM());

        SetProcessing(false);
        SetStatus("تم الانتهاء من عملية التفعيل");
    }

    private async void btnFreezeTrial_Click(object sender, EventArgs e)
    {
        if (isProcessing) return;

        SetProcessing(true);
        SetStatus("جاري تجميد فترة التجربة...");
        AppendOutput("بدء عملية تجميد فترة التجربة");
        AppendOutput("Starting trial freeze process");

        await Task.Run(() => FreezeTrial());

        SetProcessing(false);
        SetStatus("تم تجميد فترة التجربة بنجاح");
    }

    private async void btnReset_Click(object sender, EventArgs e)
    {
        if (isProcessing) return;

        var result = MessageBox.Show(
            "هل أنت متأكد من إعادة تعيين تفعيل IDM؟\nAre you sure you want to reset IDM activation?",
            "تأكيد / Confirmation",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result != DialogResult.Yes) return;

        SetProcessing(true);
        SetStatus("جاري إعادة تعيين التفعيل...");
        AppendOutput("بدء عملية إعادة تعيين التفعيل");
        AppendOutput("Starting activation reset process");

        await Task.Run(() => ResetActivation());

        SetProcessing(false);
        SetStatus("تم إعادة تعيين التفعيل بنجاح");
    }

    private async void btnDownloadIDM_Click(object sender, EventArgs e)
    {
        if (isProcessing) return;

        var result = MessageBox.Show(
            "هل تريد تحميل IDM من الموقع الرسمي؟\nDo you want to download IDM from the official website?",
            "تأكيد التحميل / Download Confirmation",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result != DialogResult.Yes) return;

        SetProcessing(true);
        SetStatus("جاري تحميل IDM...");
        AppendOutput("بدء تحميل IDM من الموقع الرسمي");
        AppendOutput("Starting IDM download from official website");

        await Task.Run(() => DownloadIDM());

        SetProcessing(false);
        SetStatus("تم الانتهاء من التحميل");
    }

    private async void btnUninstallIDM_Click(object sender, EventArgs e)
    {
        if (isProcessing) return;

        var result = MessageBox.Show(
            "تحذير: سيتم حذف IDM نهائياً من الجذور!\nهل أنت متأكد؟\n\nWarning: IDM will be completely removed from roots!\nAre you sure?",
            "تأكيد الحذف / Uninstall Confirmation",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Warning);

        if (result != DialogResult.Yes) return;

        SetProcessing(true);
        SetStatus("جاري إزالة IDM نهائياً...");
        AppendOutput("بدء عملية إزالة IDM نهائياً من الجذور");
        AppendOutput("Starting complete IDM removal from roots");

        await Task.Run(() => UninstallIDM());

        SetProcessing(false);
        SetStatus("تم حذف IDM نهائياً");
    }

    private void ActivateIDM()
    {
        try
        {
            AppendOutput("بدء عملية التفعيل الفعلية لـ IDM...");
            AppendOutput("Starting real IDM activation process...");

            // Check if IDM is installed
            string idmPath = GetIDMPath();
            if (string.IsNullOrEmpty(idmPath))
            {
                AppendOutput("خطأ: لم يتم العثور على IDM مثبت على النظام");
                AppendOutput("Error: IDM not found on the system");
                return;
            }

            AppendOutput($"تم العثور على IDM في: {idmPath}");
            AppendOutput($"IDM found at: {idmPath}");

            // Critical internet check with internetdownloadmanager.com (like original script)
            if (!CheckInternetConnectionCritical())
            {
                AppendOutput("خطأ: لا يمكن الاتصال بـ internetdownloadmanager.com، إيقاف العملية...");
                AppendOutput("Error: Unable to connect to internetdownloadmanager.com, aborting...");
                AppendOutput("المشكلة: التفعيل يتطلب اتصال بالإنترنت مع موقع IDM");
                AppendOutput("Issue: Activation requires internet connection to IDM website");
                return;
            }

            // Get system information
            GetSystemInfo();

            // Kill IDM processes
            KillIDMProcesses();

            // Create critical backup of CLSID registry keys (like original script)
            CreateCriticalRegistryBackup();

            // Follow exact sequence from original script

            // Step 1: Delete existing IDM keys (call :delete_queue)
            DeleteExistingIDMKeys();

            // Step 2: Add main activation key (call :add_key)
            AddMainActivationKey();

            // Step 3: Run PowerShell script for CLSID processing (first time)
            RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: true);

            // Step 4: Register IDM with fake serial (only if not freeze mode)
            RegisterIDMWithFakeSerial();

            // Step 4.5: Force disable registration dialog
            ForceDisableRegistrationDialog();

            // Step 5: Trigger downloads to create registry keys
            TriggerIDMDownloads();

            // Step 6: Run PowerShell script again for final locking
            RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: false);

            // Step 7: Apply ultra-powerful protection techniques
            ApplyUltraPowerfulProtection();

            // Step 8: Create multiple backup activation points
            CreateMultipleActivationBackups();

            // Step 9: Apply stealth activation techniques
            ApplyStealthActivationTechniques();

            // Step 10: Fix Error 0x800706BE specifically
            FixIDMError0x800706BE();

            // Step 11: Re-register IDM components
            ReRegisterIDMComponents();

            AppendOutput("تم إكمال عملية التفعيل الفائق بنجاح");
            AppendOutput("Ultra-powerful activation process completed successfully");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في عملية التفعيل: {ex.Message}");
            AppendOutput($"Activation error: {ex.Message}");
        }
    }

    private bool CheckInternetConnectionCritical()
    {
        try
        {
            AppendOutput("فحص الاتصال الحاسم مع internetdownloadmanager.com...");
            AppendOutput("Critical internet check with internetdownloadmanager.com...");

            // Method 1: Ping internetdownloadmanager.com (like original script)
            bool pingSuccess = false;
            try
            {
                using (var ping = new Ping())
                {
                    var reply = ping.Send("internetdownloadmanager.com", 5000);
                    if (reply.Status == IPStatus.Success)
                    {
                        pingSuccess = true;
                        AppendOutput("تم التأكد من الاتصال عبر Ping");
                        AppendOutput("Connection verified via Ping");
                    }
                }
            }
            catch
            {
                AppendOutput("فشل أمر Ping لـ internetdownloadmanager.com");
                AppendOutput("Ping command failed for internetdownloadmanager.com");
            }

            // Method 2: TCP connection to port 80 (like original script)
            bool tcpSuccess = false;
            try
            {
                using (var client = new System.Net.Sockets.TcpClient())
                {
                    client.Connect("internetdownloadmanager.com", 80);
                    if (client.Connected)
                    {
                        tcpSuccess = true;
                        AppendOutput("تم التأكد من الاتصال عبر TCP port 80");
                        AppendOutput("Connection verified via TCP port 80");
                    }
                }
            }
            catch { }

            // Must have at least one successful connection (like original script)
            if (pingSuccess || tcpSuccess)
            {
                AppendOutput("تم التأكد من الاتصال بموقع IDM الرسمي");
                AppendOutput("Connection to official IDM website verified");
                return true;
            }
            else
            {
                AppendOutput("فشل في الاتصال بـ internetdownloadmanager.com");
                AppendOutput("Failed to connect to internetdownloadmanager.com");
                AppendOutput("تحقق من اتصال الإنترنت والجدار الناري");
                AppendOutput("Check internet connection and firewall");
                return false;
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في فحص الاتصال: {ex.Message}");
            AppendOutput($"Error checking connection: {ex.Message}");
            return false;
        }
    }

    private void GetSystemInfo()
    {
        try
        {
            AppendOutput("جمع معلومات النظام...");
            AppendOutput("Gathering system information...");

            string osName = Environment.OSVersion.ToString();
            string architecture = Environment.Is64BitOperatingSystem ? "x64" : "x86";
            string idmVersion = GetIDMVersion();

            AppendOutput($"نظام التشغيل: {osName}");
            AppendOutput($"Operating System: {osName}");
            AppendOutput($"المعمارية: {architecture}");
            AppendOutput($"Architecture: {architecture}");
            AppendOutput($"إصدار IDM: {idmVersion}");
            AppendOutput($"IDM Version: {idmVersion}");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في جمع معلومات النظام: {ex.Message}");
            AppendOutput($"Error gathering system info: {ex.Message}");
        }
    }

    private string GetIDMVersion()
    {
        try
        {
            using (var key = Registry.CurrentUser.OpenSubKey(@"Software\DownloadManager"))
            {
                if (key != null)
                {
                    return key.GetValue("idmvers")?.ToString() ?? "Unknown";
                }
            }
            return "Unknown";
        }
        catch
        {
            return "Unknown";
        }
    }

    private void CreateRegistryBackup()
    {
        try
        {
            AppendOutput("إنشاء نسخة احتياطية من الريجستري...");
            AppendOutput("Creating registry backup...");

            string timestamp = DateTime.Now.ToString("yyyyMMdd-HHmmssfff");
            string tempPath = Path.GetTempPath();

            // Backup HKCU CLSID
            string backupFile1 = Path.Combine(tempPath, $"_Backup_HKCU_CLSID_{timestamp}.reg");
            ExportRegistryKey(@"HKEY_CURRENT_USER\SOFTWARE\Classes\CLSID", backupFile1);

            // Backup HKU CLSID if different
            string backupFile2 = Path.Combine(tempPath, $"_Backup_HKU_CLSID_{timestamp}.reg");
            ExportRegistryKey(@"HKEY_USERS\.DEFAULT\SOFTWARE\Classes\CLSID", backupFile2);

            AppendOutput($"تم حفظ النسخة الاحتياطية في: {tempPath}");
            AppendOutput($"Backup saved to: {tempPath}");
        }
        catch (Exception ex)
        {
            AppendOutput($"تحذير: فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            AppendOutput($"Warning: Failed to create backup: {ex.Message}");
        }
    }

    private void ExportRegistryKey(string keyPath, string filePath)
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "reg",
                    Arguments = $"export \"{keyPath}\" \"{filePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true
                }
            };

            process.Start();
            process.WaitForExit(10000);
        }
        catch (Exception ex)
        {
            AppendOutput($"فشل في تصدير {keyPath}: {ex.Message}");
            AppendOutput($"Failed to export {keyPath}: {ex.Message}");
        }
    }

    private void TestIDMDownload()
    {
        try
        {
            AppendOutput("اختبار وظيفة التحميل في IDM...");
            AppendOutput("Testing IDM download functionality...");

            // This would normally test downloading a file with IDM
            // For safety, we'll just check if IDM can be started
            string idmPath = GetIDMPath();
            if (!string.IsNullOrEmpty(idmPath))
            {
                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = idmPath,
                        Arguments = "/n",
                        UseShellExecute = false,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                Thread.Sleep(2000);

                if (!process.HasExited)
                {
                    process.Kill();
                    AppendOutput("تم التأكد من عمل IDM بنجاح");
                    AppendOutput("IDM functionality verified successfully");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"تحذير: فشل في اختبار IDM: {ex.Message}");
            AppendOutput($"Warning: Failed to test IDM: {ex.Message}");
        }
    }

    private void FreezeTrial()
    {
        try
        {
            AppendOutput("بدء عملية تجميد فترة التجربة المتقدمة...");
            AppendOutput("Starting advanced trial freeze process...");

            // Check if IDM is installed
            if (string.IsNullOrEmpty(IDMan))
            {
                AppendOutput("خطأ: لم يتم العثور على IDM مثبت على النظام");
                AppendOutput("Error: IDM not found on the system");
                return;
            }

            AppendOutput($"تم العثور على IDM في: {IDMan}");
            AppendOutput($"IDM found at: {IDMan}");

            // Critical internet check (like activation)
            if (!CheckInternetConnectionCritical())
            {
                AppendOutput("تحذير: لا يمكن الاتصال بـ internetdownloadmanager.com");
                AppendOutput("Warning: Unable to connect to internetdownloadmanager.com");
                AppendOutput("سيتم المتابعة بدون فحص الإنترنت...");
                AppendOutput("Continuing without internet check...");
            }

            // Create backup (like activation)
            CreateCriticalRegistryBackup();

            // Kill IDM processes
            KillIDMProcesses();

            // Advanced freeze sequence
            AppendOutput("تطبيق تقنيات التجميد المتقدمة...");
            AppendOutput("Applying advanced freeze techniques...");

            // Step 1: Delete existing problematic keys
            DeleteExistingIDMKeys();

            // Step 2: Add main activation key (like original script)
            AddMainActivationKey();

            // Step 3: Run CLSID processing for freeze (lock keys, no delete, with toggle)
            RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: true);

            // Step 4: Apply freeze-specific registry changes (NO REGISTRATION for freeze mode)
            ApplyAdvancedFreezeRegistry();

            // Step 4.5: Fix "لم يسجل منذ 15 يوما" message specifically
            FixRegistrationMessage();

            // Step 5: Test downloads to create registry keys
            TriggerIDMDownloads();

            // Step 6: Final CLSID processing (like original script)
            RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: false);

            // Step 7: Fix Error 0x800706BE for freeze mode
            FixIDMError0x800706BE();

            // Step 8: Re-register IDM components for freeze mode
            ReRegisterIDMComponents();

            AppendOutput("تم تجميد فترة التجربة بنجاح بالتقنيات المتقدمة");
            AppendOutput("Trial period frozen successfully with advanced techniques");
            AppendOutput("يمكنك الآن استخدام IDM بدون قيود زمنية نهائياً");
            AppendOutput("You can now use IDM without time restrictions permanently");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في عملية تجميد التجربة: {ex.Message}");
            AppendOutput($"Trial freeze error: {ex.Message}");
        }
    }

    private void ApplyAdvancedFreezeRegistry()
    {
        try
        {
            AppendOutput("تطبيق إعدادات التجميد المتقدمة...");
            AppendOutput("Applying advanced freeze settings...");

            // Advanced freeze values that prevent trial expiration
            var freezeValues = new Dictionary<string, object>
            {
                {"LstCheck", "2099-12-31"}, // Far future date
                {"LastCheckQU", "2099-12-31 23:59:59"}, // Far future date
                {"ptrk_scdt", GenerateRandomHex(new Random(), 24)}, // Random tracking data
                {"tvfrdt", GenerateRandomHex(new Random(), 16)}, // Random trial data
                {"radxcnt", 0}, // Reset trial counter
                {"CheckUpdtMnu", 0}, // Disable update check
                {"CheckAssociation", 0}, // Disable association check
                {"OnlyOnce", 0}, // Reset first run flag
                {"TrialDays", 9999}, // Set trial days to maximum
                {"InstallDate", DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd")}, // Old install date
                {"FirstRun", 0}, // Not first run
                {"TrialExpired", 0}, // Trial not expired
                {"DaysLeft", 9999}, // Maximum days left
                {"TrialMode", 0} // Not in trial mode
            };

            // Apply freeze values to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in freezeValues)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                                AppendOutput($"تم تطبيق قيمة التجميد: {kvp.Key}");
                                AppendOutput($"Applied freeze value: {kvp.Key}");
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply freeze values to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in freezeValues)
                            {
                                try
                                {
                                    if (kvp.Value is int intValue)
                                    {
                                        key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                    }
                                    else
                                    {
                                        key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                    }
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم تطبيق جميع إعدادات التجميد المتقدمة");
            AppendOutput("All advanced freeze settings applied");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق إعدادات التجميد: {ex.Message}");
            AppendOutput($"Error applying freeze settings: {ex.Message}");
        }
    }

    private void ResetActivation()
    {
        try
        {
            AppendOutput("بدء عملية إعادة تعيين التفعيل...");
            AppendOutput("Starting activation reset...");

            // Kill IDM processes
            KillIDMProcesses();

            // Reset registry keys
            ResetIDMRegistry();

            AppendOutput("تم إعادة تعيين التفعيل بنجاح");
            AppendOutput("Activation reset successfully");
            AppendOutput("يمكنك الآن إعادة تفعيل IDM أو تجميد فترة التجربة");
            AppendOutput("You can now reactivate IDM or freeze the trial period");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في عملية إعادة التعيين: {ex.Message}");
            AppendOutput($"Reset error: {ex.Message}");
        }
    }

    private string GetIDMPath()
    {
        try
        {
            // Check common installation paths
            string[] paths = {
                @"C:\Program Files (x86)\Internet Download Manager\IDMan.exe",
                @"C:\Program Files\Internet Download Manager\IDMan.exe"
            };

            foreach (string path in paths)
            {
                if (File.Exists(path))
                    return path;
            }

            // Check registry for installation path
            using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager"))
            {
                if (key != null)
                {
                    string installLocation = key.GetValue("InstallLocation")?.ToString();
                    if (!string.IsNullOrEmpty(installLocation))
                    {
                        string idmPath = Path.Combine(installLocation, "IDMan.exe");
                        if (File.Exists(idmPath))
                            return idmPath;
                    }
                }
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    private void KillIDMProcesses()
    {
        try
        {
            AppendOutput("إيقاف عمليات IDM...");
            AppendOutput("Stopping IDM processes...");

            string[] processNames = { "IDMan", "IEMonitor", "IDMGrHlp" };

            foreach (string processName in processNames)
            {
                var processes = Process.GetProcessesByName(processName);
                foreach (var process in processes)
                {
                    try
                    {
                        process.Kill();
                        process.WaitForExit(5000);
                        AppendOutput($"تم إيقاف العملية: {processName}");
                        AppendOutput($"Stopped process: {processName}");
                    }
                    catch (Exception ex)
                    {
                        AppendOutput($"فشل في إيقاف العملية {processName}: {ex.Message}");
                        AppendOutput($"Failed to stop process {processName}: {ex.Message}");
                    }
                }
            }

            Thread.Sleep(2000); // Wait for processes to fully terminate
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إيقاف العمليات: {ex.Message}");
            AppendOutput($"Error stopping processes: {ex.Message}");
        }
    }

    private void ApplyActivationRegistry()
    {
        try
        {
            AppendOutput("تطبيق إعدادات التفعيل في الريجستري...");
            AppendOutput("Applying activation registry settings...");

            // Add the main registry key for IDM activation
            string regPath = @"HKEY_LOCAL_MACHINE\SOFTWARE\Internet Download Manager";

            try
            {
                using (var key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Internet Download Manager"))
                {
                    if (key != null)
                    {
                        key.SetValue("AdvIntDriverEnabled2", 1, RegistryValueKind.DWord);
                        AppendOutput("تم إضافة مفتاح AdvIntDriverEnabled2");
                        AppendOutput("Added AdvIntDriverEnabled2 key");
                    }
                }
            }
            catch (Exception ex)
            {
                AppendOutput($"خطأ في إضافة مفتاح التفعيل: {ex.Message}");
                AppendOutput($"Error adding activation key: {ex.Message}");
            }

            // Register IDM CLSID keys (simplified version)
            RegisterIDMCLSID();

            AppendOutput("تم تطبيق إعدادات التفعيل");
            AppendOutput("Activation settings applied");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق إعدادات التفعيل: {ex.Message}");
            AppendOutput($"Error applying activation settings: {ex.Message}");
        }
    }

    private void RegisterIDMCLSID()
    {
        try
        {
            AppendOutput("تسجيل CLSID keys لـ IDM...");
            AppendOutput("Registering IDM CLSID keys...");

            // Determine architecture
            bool is64Bit = Environment.Is64BitOperatingSystem;
            string clsidPath = is64Bit ? @"SOFTWARE\Classes\Wow6432Node\CLSID" : @"SOFTWARE\Classes\CLSID";

            // Generate a random CLSID for IDM
            string randomClsid = "{" + Guid.NewGuid().ToString().ToUpper() + "}";

            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey($@"{clsidPath}\{randomClsid}"))
                {
                    if (key != null)
                    {
                        // Set a random number as default value (IDM pattern)
                        Random rand = new Random();
                        key.SetValue("", rand.Next(100000, 999999).ToString());

                        AppendOutput($"تم تسجيل CLSID: {randomClsid}");
                        AppendOutput($"Registered CLSID: {randomClsid}");
                    }
                }
            }
            catch (Exception ex)
            {
                AppendOutput($"خطأ في تسجيل CLSID: {ex.Message}");
                AppendOutput($"Error registering CLSID: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تسجيل CLSID keys: {ex.Message}");
            AppendOutput($"Error registering CLSID keys: {ex.Message}");
        }
    }

    private void ApplyFreezeTrialRegistry()
    {
        try
        {
            AppendOutput("تطبيق إعدادات تجميد فترة التجربة...");
            AppendOutput("Applying trial freeze settings...");

            // Find and lock IDM CLSID registry keys
            FindAndLockIDMKeys();

            AppendOutput("تم تجميد فترة التجربة في الريجستري");
            AppendOutput("Trial period frozen in registry");

            AppendOutput("تم حفظ إعدادات التجميد");
            AppendOutput("Freeze settings saved");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تجميد فترة التجربة: {ex.Message}");
            AppendOutput($"Error freezing trial period: {ex.Message}");
        }
    }

    private void FindAndLockIDMKeys()
    {
        try
        {
            AppendOutput("البحث المتقدم عن مفاتيح IDM CLSID...");
            AppendOutput("Advanced search for IDM CLSID keys...");

            // Use the advanced system variables
            if (string.IsNullOrEmpty(CLSID) || string.IsNullOrEmpty(_sid))
            {
                AppendOutput("خطأ: لم يتم تهيئة النظام المتقدم بشكل صحيح");
                AppendOutput("Error: Advanced system not properly initialized");
                return;
            }

            // Clear previous results
            finalValues.Clear();

            // Advanced CLSID key search (like original PowerShell script)
            AdvancedCLSIDSearch();

            if (finalValues.Count > 0)
            {
                AppendOutput($"تم العثور على {finalValues.Count} مفتاح IDM");
                AppendOutput($"Found {finalValues.Count} IDM keys");

                // Check if too many keys (like original script)
                if (finalValues.Count > 20)
                {
                    AppendOutput("عدد المفاتيح أكثر من 20، سيتم حذفها بدلاً من قفلها...");
                    AppendOutput("Key count is more than 20, deleting them instead of locking...");

                    // Delete instead of lock
                    DeleteIDMKeys();
                }
                else
                {
                    // Lock the keys using advanced techniques
                    LockIDMKeysAdvanced();
                }
            }
            else
            {
                AppendOutput("لم يتم العثور على مفاتيح IDM CLSID");
                AppendOutput("No IDM CLSID keys found");

                // Create dummy keys for trial freeze
                CreateDummyIDMKeysAdvanced();
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في البحث المتقدم عن مفاتيح IDM: {ex.Message}");
            AppendOutput($"Error in advanced IDM key search: {ex.Message}");
        }
    }

    private void AdvancedCLSIDSearch()
    {
        try
        {
            AppendOutput("تطبيق خوارزمية البحث المتقدمة...");
            AppendOutput("Applying advanced search algorithm...");

            // Search in HKCU path
            SearchInRegistryPath(CLSID, "HKCU");

            // Search in HKU path if not synced (like original script)
            if (!HKCUsync)
            {
                SearchInRegistryPath(CLSID2, "HKU");
            }

            // Remove duplicates (like original script)
            finalValues = finalValues.Distinct().ToList();

            AppendOutput($"تم العثور على {finalValues.Count} مفتاح فريد");
            AppendOutput($"Found {finalValues.Count} unique keys");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في البحث المتقدم: {ex.Message}");
            AppendOutput($"Error in advanced search: {ex.Message}");
        }
    }

    private void SearchInRegistryPath(string regPath, string rootName)
    {
        try
        {
            AppendOutput($"البحث في {regPath}");
            AppendOutput($"Searching in {regPath}");

            RegistryKey rootKey;
            string subPath;

            if (rootName == "HKCU")
            {
                rootKey = Registry.CurrentUser;
                subPath = regPath.Replace("HKCU\\", "");
            }
            else
            {
                rootKey = Registry.Users;
                subPath = regPath.Replace("HKU\\", "");
            }

            using (var clsidKey = rootKey.OpenSubKey(subPath))
            {
                if (clsidKey != null)
                {
                    foreach (string subKeyName in clsidKey.GetSubKeyNames())
                    {
                        // Check if it's a GUID format (like original script)
                        if (IsGuidFormat(subKeyName))
                        {
                            try
                            {
                                using (var subKey = clsidKey.OpenSubKey(subKeyName))
                                {
                                    if (subKey != null && IsIDMKeyAdvanced(subKey, subKeyName))
                                    {
                                        finalValues.Add(subKeyName);
                                    }
                                }
                            }
                            catch
                            {
                                // Locked key - add it to list (like original script)
                                finalValues.Add(subKeyName);
                                AppendOutput($"{subKeyName} - Found Locked Key");
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في البحث في {regPath}: {ex.Message}");
            AppendOutput($"Error searching in {regPath}: {ex.Message}");
        }
    }

    private bool IsIDMKeyAdvanced(RegistryKey key, string keyName)
    {
        try
        {
            // Advanced IDM key detection (like original PowerShell script)

            // Check default value for digits (like original script)
            var defaultValue = key.GetValue("")?.ToString();
            if (!string.IsNullOrEmpty(defaultValue))
            {
                if (defaultValue.All(char.IsDigit) && key.SubKeyCount == 0)
                {
                    AppendOutput($"{keyName} - Found Digit In Default and No Subkeys");
                    return true;
                }

                if ((defaultValue.Contains("+") || defaultValue.Contains("=")) && key.SubKeyCount == 0)
                {
                    AppendOutput($"{keyName} - Found + or = In Default and No Subkeys");
                    return true;
                }
            }

            // Check Version subkey (like original script)
            try
            {
                using (var versionKey = key.OpenSubKey("Version"))
                {
                    if (versionKey != null)
                    {
                        var versionValue = versionKey.GetValue("")?.ToString();
                        if (!string.IsNullOrEmpty(versionValue) && versionValue.All(char.IsDigit) && key.SubKeyCount == 1)
                        {
                            AppendOutput($"{keyName} - Found Digit In \\Version and No Other Subkeys");
                            return true;
                        }
                    }
                }
            }
            catch { }

            // Check for IDM-specific value names (like original script)
            foreach (string valueName in key.GetValueNames())
            {
                if (valueName.Contains("MData") || valueName.Contains("Model") ||
                    valueName.Contains("scansk") || valueName.Contains("Therad"))
                {
                    AppendOutput($"{keyName} - Found MData Model scansk Therad");
                    return true;
                }
            }

            // Check for empty key (like original script)
            if (key.ValueCount == 0 && key.SubKeyCount == 0)
            {
                AppendOutput($"{keyName} - Found Empty Key");
                return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private void SearchIDMKeysInRegistry(RegistryKey rootKey, string clsidPath, List<string> idmKeys)
    {
        try
        {
            using (var clsidKey = rootKey.OpenSubKey(clsidPath))
            {
                if (clsidKey != null)
                {
                    foreach (string subKeyName in clsidKey.GetSubKeyNames())
                    {
                        if (IsGuidFormat(subKeyName))
                        {
                            try
                            {
                                using (var subKey = clsidKey.OpenSubKey(subKeyName))
                                {
                                    if (subKey != null && IsIDMKey(subKey))
                                    {
                                        idmKeys.Add(subKeyName);
                                        AppendOutput($"تم العثور على مفتاح IDM: {subKeyName}");
                                        AppendOutput($"Found IDM key: {subKeyName}");
                                    }
                                }
                            }
                            catch
                            {
                                // Key might be locked or inaccessible
                                continue;
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في البحث في الريجستري: {ex.Message}");
            AppendOutput($"Error searching registry: {ex.Message}");
        }
    }

    private bool IsGuidFormat(string input)
    {
        return Guid.TryParse(input, out _);
    }

    private bool IsIDMKey(RegistryKey key)
    {
        try
        {
            // Check for IDM patterns
            var defaultValue = key.GetValue("")?.ToString();

            // IDM keys often have numeric default values
            if (!string.IsNullOrEmpty(defaultValue) && defaultValue.All(char.IsDigit))
            {
                return true;
            }

            // Check for IDM-specific value names
            foreach (string valueName in key.GetValueNames())
            {
                if (valueName.Contains("MData") || valueName.Contains("Model") ||
                    valueName.Contains("scansk") || valueName.Contains("Therad"))
                {
                    return true;
                }
            }

            // Check if key has no subkeys and no values (empty key pattern)
            if (key.SubKeyCount == 0 && key.ValueCount == 0)
            {
                return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    private void LockRegistryKeys(List<string> keyNames, string clsidPath)
    {
        try
        {
            AppendOutput("قفل مفاتيح IDM بتقنيات متقدمة...");
            AppendOutput("Locking IDM keys with advanced techniques...");

            // Enable required privileges
            EnableRequiredPrivileges();

            foreach (string keyName in keyNames)
            {
                try
                {
                    string fullPath = $@"{clsidPath}\{keyName}";

                    // Create the key if it doesn't exist
                    using (var key = Registry.CurrentUser.CreateSubKey(fullPath))
                    {
                        if (key != null)
                        {
                            // Add marker value
                            key.SetValue("IAS_Frozen", DateTime.Now.ToString(), RegistryValueKind.String);
                        }
                    }

                    // Apply advanced locking using Take-Permissions technique
                    if (TakePermissionsAndLock("CurrentUser", fullPath))
                    {
                        AppendOutput($"تم قفل المفتاح بنجاح: {keyName}");
                        AppendOutput($"Successfully locked key: {keyName}");
                    }
                    else
                    {
                        AppendOutput($"فشل في قفل المفتاح: {keyName}");
                        AppendOutput($"Failed to lock key: {keyName}");
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"خطأ في قفل المفتاح {keyName}: {ex.Message}");
                    AppendOutput($"Error locking key {keyName}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في عملية القفل: {ex.Message}");
            AppendOutput($"Error in locking process: {ex.Message}");
        }
    }

    private void EnableRequiredPrivileges()
    {
        try
        {
            AppendOutput("تفعيل الصلاحيات المطلوبة...");
            AppendOutput("Enabling required privileges...");

            // Enable SE_BACKUP_PRIVILEGE, SE_RESTORE_PRIVILEGE, SE_TAKE_OWNERSHIP_PRIVILEGE
            bool previousValue;
            RtlAdjustPrivilege(SE_TAKE_OWNERSHIP_PRIVILEGE, true, false, out previousValue);
            RtlAdjustPrivilege(SE_BACKUP_PRIVILEGE, true, false, out previousValue);
            RtlAdjustPrivilege(SE_RESTORE_PRIVILEGE, true, false, out previousValue);

            AppendOutput("تم تفعيل الصلاحيات المطلوبة");
            AppendOutput("Required privileges enabled");
        }
        catch (Exception ex)
        {
            AppendOutput($"تحذير: فشل في تفعيل الصلاحيات: {ex.Message}");
            AppendOutput($"Warning: Failed to enable privileges: {ex.Message}");
        }
    }

    private bool TakePermissionsAndLock(string rootKey, string regKey)
    {
        try
        {
            // Get the registry key
            RegistryKey baseKey = rootKey == "CurrentUser" ? Registry.CurrentUser : Registry.Users;

            using (var key = baseKey.OpenSubKey(regKey, RegistryKeyPermissionCheck.ReadWriteSubTree, RegistryRights.TakeOwnership))
            {
                if (key == null) return false;

                // Get current security descriptor
                var security = key.GetAccessControl();

                // Set owner to Administrators
                var adminSid = new SecurityIdentifier(WellKnownSidType.BuiltinAdministratorsSid, null);
                security.SetOwner(adminSid);
                key.SetAccessControl(security);

                // Open key with ChangePermissions rights
                using (var keyForPermissions = baseKey.OpenSubKey(regKey, RegistryKeyPermissionCheck.ReadWriteSubTree, RegistryRights.ChangePermissions))
                {
                    if (keyForPermissions == null) return false;

                    var newSecurity = keyForPermissions.GetAccessControl();

                    // Remove all existing access rules
                    var existingRules = newSecurity.GetAccessRules(true, true, typeof(SecurityIdentifier));
                    foreach (RegistryAccessRule rule in existingRules)
                    {
                        newSecurity.RemoveAccessRule(rule);
                    }

                    // Add deny rule for Everyone
                    var everyoneSid = new SecurityIdentifier(WellKnownSidType.WorldSid, null);
                    var denyRule = new RegistryAccessRule(everyoneSid, RegistryRights.FullControl, AccessControlType.Deny);
                    newSecurity.SetAccessRule(denyRule);

                    // Set the new security descriptor
                    keyForPermissions.SetAccessControl(newSecurity);

                    return true;
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق القفل المتقدم: {ex.Message}");
            AppendOutput($"Error applying advanced lock: {ex.Message}");
            return false;
        }
    }

    private void CreateDummyIDMKeys(string clsidPath)
    {
        try
        {
            AppendOutput("إنشاء مفاتيح وهمية لتجميد التجربة...");
            AppendOutput("Creating dummy keys for trial freeze...");

            Random rand = new Random();

            for (int i = 0; i < 5; i++)
            {
                string dummyGuid = "{" + Guid.NewGuid().ToString().ToUpper() + "}";

                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey($@"{clsidPath}\{dummyGuid}"))
                    {
                        if (key != null)
                        {
                            key.SetValue("", rand.Next(100000, 999999).ToString());
                            key.SetValue("IAS_Frozen", DateTime.Now.ToString());
                            AppendOutput($"تم إنشاء مفتاح وهمي: {dummyGuid}");
                            AppendOutput($"Created dummy key: {dummyGuid}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"فشل في إنشاء مفتاح وهمي: {ex.Message}");
                    AppendOutput($"Failed to create dummy key: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء المفاتيح الوهمية: {ex.Message}");
            AppendOutput($"Error creating dummy keys: {ex.Message}");
        }
    }

    private void ResetIDMRegistry()
    {
        try
        {
            AppendOutput("إعادة تعيين إعدادات الريجستري...");
            AppendOutput("Resetting registry settings...");

            // Reset IDM CLSID keys
            ResetIDMCLSIDKeys();

            // Reset main IDM registry entries
            ResetMainIDMEntries();

            AppendOutput("تم إعادة تعيين جميع الإعدادات");
            AppendOutput("All settings reset successfully");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تعيين الريجستري: {ex.Message}");
            AppendOutput($"Error resetting registry: {ex.Message}");
        }
    }

    private void ResetIDMCLSIDKeys()
    {
        try
        {
            AppendOutput("إعادة تعيين مفاتيح IDM CLSID...");
            AppendOutput("Resetting IDM CLSID keys...");

            bool is64Bit = Environment.Is64BitOperatingSystem;
            string clsidPath = is64Bit ? @"SOFTWARE\Classes\Wow6432Node\CLSID" : @"SOFTWARE\Classes\CLSID";

            List<string> keysToDelete = new List<string>();

            // Find IDM keys to delete
            using (var clsidKey = Registry.CurrentUser.OpenSubKey(clsidPath))
            {
                if (clsidKey != null)
                {
                    foreach (string subKeyName in clsidKey.GetSubKeyNames())
                    {
                        if (IsGuidFormat(subKeyName))
                        {
                            try
                            {
                                using (var subKey = clsidKey.OpenSubKey(subKeyName))
                                {
                                    if (subKey != null)
                                    {
                                        // Check if this is an IDM key or frozen key
                                        if (IsIDMKey(subKey) || subKey.GetValue("IAS_Frozen") != null)
                                        {
                                            keysToDelete.Add(subKeyName);
                                        }
                                    }
                                }
                            }
                            catch
                            {
                                // Key might be locked, add it to deletion list
                                keysToDelete.Add(subKeyName);
                            }
                        }
                    }
                }
            }

            // Delete found keys (including locked ones)
            foreach (string keyName in keysToDelete)
            {
                try
                {
                    string fullPath = $@"{clsidPath}\{keyName}";

                    // Try normal deletion first
                    try
                    {
                        Registry.CurrentUser.DeleteSubKeyTree(fullPath, false);
                        AppendOutput($"تم حذف المفتاح: {keyName}");
                        AppendOutput($"Deleted key: {keyName}");
                        continue;
                    }
                    catch
                    {
                        // Key might be locked, try advanced deletion
                        AppendOutput($"المفتاح مقفل، محاولة الحذف المتقدم: {keyName}");
                        AppendOutput($"Key is locked, trying advanced deletion: {keyName}");
                    }

                    // Enable privileges and take ownership
                    EnableRequiredPrivileges();

                    if (TakeOwnershipAndDelete("CurrentUser", fullPath))
                    {
                        AppendOutput($"تم حذف المفتاح المقفل: {keyName}");
                        AppendOutput($"Deleted locked key: {keyName}");
                    }
                    else
                    {
                        AppendOutput($"فشل في حذف المفتاح المقفل: {keyName}");
                        AppendOutput($"Failed to delete locked key: {keyName}");
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"خطأ في حذف المفتاح {keyName}: {ex.Message}");
                    AppendOutput($"Error deleting key {keyName}: {ex.Message}");
                }
            }

            AppendOutput($"تم حذف {keysToDelete.Count} مفتاح IDM");
            AppendOutput($"Deleted {keysToDelete.Count} IDM keys");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تعيين مفاتيح CLSID: {ex.Message}");
            AppendOutput($"Error resetting CLSID keys: {ex.Message}");
        }
    }

    private void ResetMainIDMEntries()
    {
        try
        {
            AppendOutput("إعادة تعيين إدخالات IDM الرئيسية...");
            AppendOutput("Resetting main IDM entries...");

            // Reset HKLM entries
            try
            {
                using (var key = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Internet Download Manager", true))
                {
                    if (key != null)
                    {
                        try
                        {
                            key.DeleteValue("AdvIntDriverEnabled2", false);
                            AppendOutput("تم حذف AdvIntDriverEnabled2");
                            AppendOutput("Deleted AdvIntDriverEnabled2");
                        }
                        catch { }
                    }
                }
            }
            catch (Exception ex)
            {
                AppendOutput($"خطأ في إعادة تعيين HKLM: {ex.Message}");
                AppendOutput($"Error resetting HKLM: {ex.Message}");
            }

            // Reset HKCU entries
            try
            {
                Registry.CurrentUser.DeleteSubKeyTree(@"SOFTWARE\DownloadManager", false);
                AppendOutput("تم حذف مجلد DownloadManager");
                AppendOutput("Deleted DownloadManager folder");
            }
            catch (Exception ex)
            {
                AppendOutput($"خطأ في حذف DownloadManager: {ex.Message}");
                AppendOutput($"Error deleting DownloadManager: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تعيين الإدخالات الرئيسية: {ex.Message}");
            AppendOutput($"Error resetting main entries: {ex.Message}");
        }
    }

    private void DownloadIDM()
    {
        try
        {
            AppendOutput("فتح الموقع الرسمي لـ IDM...");
            AppendOutput("Opening IDM official website...");

            // Open the official IDM download page
            string idmDownloadUrl = "https://www.internetdownloadmanager.com/download.html";

            AppendOutput($"فتح الرابط: {idmDownloadUrl}");
            AppendOutput($"Opening URL: {idmDownloadUrl}");

            // Open the URL in the default browser
            Process.Start(new ProcessStartInfo
            {
                FileName = idmDownloadUrl,
                UseShellExecute = true
            });

            AppendOutput("تم فتح صفحة التحميل الرسمية");
            AppendOutput("Official download page opened");
            AppendOutput("قم بتحميل أحدث إصدار من IDM من الموقع");
            AppendOutput("Download the latest version of IDM from the website");

            // Alternative: Direct download link (if available)
            AppendOutput("يمكنك أيضاً تحميل IDM مباشرة من:");
            AppendOutput("You can also download IDM directly from:");
            AppendOutput("https://www.internetdownloadmanager.com/");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في فتح صفحة التحميل: {ex.Message}");
            AppendOutput($"Error opening download page: {ex.Message}");
            AppendOutput("يرجى زيارة الموقع يدوياً: https://www.internetdownloadmanager.com/");
            AppendOutput("Please visit the website manually: https://www.internetdownloadmanager.com/");
        }
    }

    private void UninstallIDM()
    {
        try
        {
            AppendOutput("بدء عملية إزالة IDM نهائياً...");
            AppendOutput("Starting complete IDM removal...");

            // Step 1: Kill all IDM processes
            KillIDMProcesses();

            // Step 2: Uninstall via Windows Programs and Features
            UninstallIDMViaRegistry();

            // Step 3: Remove installation directories
            RemoveIDMDirectories();

            // Step 4: Clean registry entries
            CleanIDMRegistry();

            // Step 5: Remove browser extensions
            RemoveIDMBrowserExtensions();

            AppendOutput("تم حذف IDM نهائياً من النظام");
            AppendOutput("IDM completely removed from the system");
            AppendOutput("يُنصح بإعادة تشغيل الكمبيوتر");
            AppendOutput("It's recommended to restart the computer");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في عملية الإزالة: {ex.Message}");
            AppendOutput($"Error during removal process: {ex.Message}");
        }
    }

    private void UninstallIDMViaRegistry()
    {
        try
        {
            AppendOutput("البحث عن معلومات إلغاء التثبيت...");
            AppendOutput("Looking for uninstall information...");

            string[] uninstallKeys = {
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager"
            };

            foreach (string keyPath in uninstallKeys)
            {
                try
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(keyPath))
                    {
                        if (key != null)
                        {
                            string uninstallString = key.GetValue("UninstallString")?.ToString();
                            if (!string.IsNullOrEmpty(uninstallString))
                            {
                                AppendOutput($"تم العثور على أداة إلغاء التثبيت: {uninstallString}");
                                AppendOutput($"Found uninstaller: {uninstallString}");

                                // Run the uninstaller silently
                                if (uninstallString.Contains("\""))
                                {
                                    string exePath = uninstallString.Split('"')[1];
                                    Process.Start(new ProcessStartInfo
                                    {
                                        FileName = exePath,
                                        Arguments = "/S", // Silent uninstall
                                        UseShellExecute = false,
                                        CreateNoWindow = true
                                    });
                                }

                                AppendOutput("تم تشغيل أداة إلغاء التثبيت");
                                AppendOutput("Uninstaller executed");
                                Thread.Sleep(5000); // Wait for uninstaller to complete
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"خطأ في معالجة {keyPath}: {ex.Message}");
                    AppendOutput($"Error processing {keyPath}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إلغاء التثبيت عبر الريجستري: {ex.Message}");
            AppendOutput($"Error uninstalling via registry: {ex.Message}");
        }
    }

    private void RemoveIDMDirectories()
    {
        try
        {
            AppendOutput("إزالة مجلدات IDM...");
            AppendOutput("Removing IDM directories...");

            string[] idmPaths = {
                @"C:\Program Files\Internet Download Manager",
                @"C:\Program Files (x86)\Internet Download Manager",
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "IDM"),
                Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "IDM")
            };

            foreach (string path in idmPaths)
            {
                try
                {
                    if (Directory.Exists(path))
                    {
                        AppendOutput($"حذف المجلد: {path}");
                        AppendOutput($"Deleting directory: {path}");

                        Directory.Delete(path, true);

                        AppendOutput($"تم حذف: {path}");
                        AppendOutput($"Deleted: {path}");
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"فشل في حذف {path}: {ex.Message}");
                    AppendOutput($"Failed to delete {path}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة المجلدات: {ex.Message}");
            AppendOutput($"Error removing directories: {ex.Message}");
        }
    }

    private void CleanIDMRegistry()
    {
        try
        {
            AppendOutput("تنظيف إدخالات الريجستري...");
            AppendOutput("Cleaning registry entries...");

            string[] registryPaths = {
                @"SOFTWARE\DownloadManager",
                @"SOFTWARE\Internet Download Manager",
                @"SOFTWARE\WOW6432Node\Internet Download Manager",
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager"
            };

            // Clean Current User registry
            foreach (string regPath in registryPaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.OpenSubKey(regPath, true))
                    {
                        if (key != null)
                        {
                            AppendOutput($"حذف مفتاح HKCU: {regPath}");
                            AppendOutput($"Deleting HKCU key: {regPath}");

                            Registry.CurrentUser.DeleteSubKeyTree(regPath, false);
                        }
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"فشل في حذف HKCU\\{regPath}: {ex.Message}");
                }
            }

            // Clean Local Machine registry
            foreach (string regPath in registryPaths)
            {
                try
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(regPath, true))
                    {
                        if (key != null)
                        {
                            AppendOutput($"حذف مفتاح HKLM: {regPath}");
                            AppendOutput($"Deleting HKLM key: {regPath}");

                            Registry.LocalMachine.DeleteSubKeyTree(regPath, false);
                        }
                    }
                }
                catch (Exception ex)
                {
                    AppendOutput($"فشل في حذف HKLM\\{regPath}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تنظيف الريجستري: {ex.Message}");
            AppendOutput($"Error cleaning registry: {ex.Message}");
        }
    }

    private void RemoveIDMBrowserExtensions()
    {
        try
        {
            AppendOutput("إزالة إضافات المتصفح...");
            AppendOutput("Removing browser extensions...");

            // Chrome extension removal
            RemoveChromeIDMExtension();

            // Firefox extension removal
            RemoveFirefoxIDMExtension();

            AppendOutput("تم الانتهاء من إزالة إضافات المتصفح");
            AppendOutput("Browser extensions removal completed");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة إضافات المتصفح: {ex.Message}");
            AppendOutput($"Error removing browser extensions: {ex.Message}");
        }
    }

    private void RemoveChromeIDMExtension()
    {
        try
        {
            string chromeExtensionsPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                @"Google\Chrome\User Data\Default\Extensions"
            );

            if (Directory.Exists(chromeExtensionsPath))
            {
                string[] idmExtensionIds = { "ngpampappnmepgilojfohadhhmbhlaek", "ljdobmomdgdljniojadhoplhkpialdid" };

                foreach (string extensionId in idmExtensionIds)
                {
                    string extensionPath = Path.Combine(chromeExtensionsPath, extensionId);
                    if (Directory.Exists(extensionPath))
                    {
                        Directory.Delete(extensionPath, true);
                        AppendOutput($"تم حذف إضافة Chrome: {extensionId}");
                        AppendOutput($"Deleted Chrome extension: {extensionId}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة إضافة Chrome: {ex.Message}");
        }
    }

    private void RemoveFirefoxIDMExtension()
    {
        try
        {
            string firefoxProfilesPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                @"Mozilla\Firefox\Profiles"
            );

            if (Directory.Exists(firefoxProfilesPath))
            {
                var profileDirs = Directory.GetDirectories(firefoxProfilesPath);
                foreach (string profileDir in profileDirs)
                {
                    string extensionsPath = Path.Combine(profileDir, "extensions");
                    if (Directory.Exists(extensionsPath))
                    {
                        var extensionFiles = Directory.GetFiles(extensionsPath, "*idm*", SearchOption.AllDirectories);
                        foreach (string file in extensionFiles)
                        {
                            File.Delete(file);
                            AppendOutput($"تم حذف ملف Firefox: {Path.GetFileName(file)}");
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة إضافة Firefox: {ex.Message}");
        }
    }

    private bool TakeOwnershipAndDelete(string rootKey, string regKey)
    {
        try
        {
            // Get the registry key
            RegistryKey baseKey = rootKey == "CurrentUser" ? Registry.CurrentUser : Registry.Users;

            // Take ownership first
            using (var key = baseKey.OpenSubKey(regKey, RegistryKeyPermissionCheck.ReadWriteSubTree, RegistryRights.TakeOwnership))
            {
                if (key == null) return false;

                var security = key.GetAccessControl();
                var adminSid = new SecurityIdentifier(WellKnownSidType.BuiltinAdministratorsSid, null);
                security.SetOwner(adminSid);
                key.SetAccessControl(security);
            }

            // Grant full control to administrators
            using (var key = baseKey.OpenSubKey(regKey, RegistryKeyPermissionCheck.ReadWriteSubTree, RegistryRights.ChangePermissions))
            {
                if (key == null) return false;

                var security = key.GetAccessControl();
                var adminSid = new SecurityIdentifier(WellKnownSidType.BuiltinAdministratorsSid, null);
                var allowRule = new RegistryAccessRule(adminSid, RegistryRights.FullControl, AccessControlType.Allow);
                security.SetAccessRule(allowRule);
                key.SetAccessControl(security);
            }

            // Now try to delete the key
            try
            {
                baseKey.DeleteSubKeyTree(regKey, false);
                return true;
            }
            catch
            {
                // If still can't delete, it might be in use
                return false;
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في أخذ الملكية والحذف: {ex.Message}");
            AppendOutput($"Error taking ownership and deleting: {ex.Message}");
            return false;
        }
    }



    private string GenerateFakeSerial()
    {
        try
        {
            // Generate 20 random characters (like original script)
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            Random rand = new Random();
            string key = new string(Enumerable.Repeat(chars, 20)
                .Select(s => s[rand.Next(s.Length)]).ToArray());

            // Format as XXXXX-XXXXX-XXXXX-XXXXX (like original script)
            return $"{key.Substring(0, 5)}-{key.Substring(5, 5)}-{key.Substring(10, 5)}-{key.Substring(15, 5)}";
        }
        catch
        {
            return "ABCDE-FGHIJ-KLMNO-PQRST"; // Fallback
        }
    }

    private string GetCurrentUserSid()
    {
        try
        {
            var identity = WindowsIdentity.GetCurrent();
            return identity.User?.Value ?? "";
        }
        catch
        {
            return "";
        }
    }

    private void TriggerIDMDownloads()
    {
        try
        {
            AppendOutput("تشغيل تحميلات لإنشاء مفاتيح ريجستري معينة...");
            AppendOutput("Triggering downloads to create certain registry keys...");

            string idmPath = GetIDMPath();
            if (string.IsNullOrEmpty(idmPath))
            {
                AppendOutput("تحذير: لم يتم العثور على IDM لتشغيل التحميلات");
                AppendOutput("Warning: IDM not found to trigger downloads");
                return;
            }

            string tempPath = Path.GetTempPath();
            string tempFile = Path.Combine(tempPath, "temp.png");

            // URLs from original script
            string[] downloadUrls = {
                "https://www.internetdownloadmanager.com/images/idm_box_min.png",
                "https://www.internetdownloadmanager.com/register/IDMlib/images/idman_logos.png",
                "https://www.internetdownloadmanager.com/pictures/idm_about.png"
            };

            foreach (string url in downloadUrls)
            {
                try
                {
                    AppendOutput($"تحميل: {url}");
                    AppendOutput($"Downloading: {url}");

                    // Delete existing file
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }

                    // Start IDM download (like original script)
                    var process = new Process
                    {
                        StartInfo = new ProcessStartInfo
                        {
                            FileName = idmPath,
                            Arguments = $"/n /d \"{url}\" /p \"{tempPath}\" /f temp.png",
                            UseShellExecute = false,
                            CreateNoWindow = true
                        }
                    };

                    process.Start();

                    // Wait for file to be created (like original script)
                    int attempts = 0;
                    while (attempts < 20 && !File.Exists(tempFile))
                    {
                        Thread.Sleep(1000);
                        attempts++;
                    }

                    if (File.Exists(tempFile))
                    {
                        AppendOutput("تم التحميل بنجاح");
                        AppendOutput("Download successful");
                        File.Delete(tempFile); // Clean up
                    }
                    else
                    {
                        AppendOutput("انتهت مهلة التحميل");
                        AppendOutput("Download timeout");
                    }

                }
                catch (Exception ex)
                {
                    AppendOutput($"خطأ في التحميل: {ex.Message}");
                    AppendOutput($"Download error: {ex.Message}");
                }
            }

            // Kill IDM processes after downloads (like original script)
            Thread.Sleep(3000);
            KillIDMProcesses();

            AppendOutput("تم الانتهاء من التحميلات التجريبية");
            AppendOutput("Test downloads completed");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تشغيل التحميلات: {ex.Message}");
            AppendOutput($"Error triggering downloads: {ex.Message}");
        }
    }

    private void InitializeAdvancedSystem()
    {
        try
        {
            AppendOutput("تهيئة النظام المتقدم...");
            AppendOutput("Initializing advanced system...");

            // Get user account SID (like original script)
            GetUserAccountSID();

            // Check HKCUsync (like original script)
            CheckHKCUsync();

            // Set architecture and registry paths (like original script)
            SetArchitectureAndPaths();

            // Get IDM path from registry (like original script)
            GetIDMPathFromRegistry();

            AppendOutput("تم تهيئة النظام المتقدم بنجاح");
            AppendOutput("Advanced system initialized successfully");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تهيئة النظام المتقدم: {ex.Message}");
            AppendOutput($"Error initializing advanced system: {ex.Message}");
        }
    }

    private void GetUserAccountSID()
    {
        try
        {
            AppendOutput("الحصول على SID المستخدم...");
            AppendOutput("Getting user account SID...");

            // Method 1: Using current user (like original script)
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                _sid = identity.User?.Value ?? "";

                if (!string.IsNullOrEmpty(_sid))
                {
                    // Test if SID works
                    using (var key = Registry.Users.OpenSubKey($@"{_sid}\Software"))
                    {
                        if (key != null)
                        {
                            AppendOutput($"تم الحصول على SID: {_sid}");
                            AppendOutput($"Got SID: {_sid}");
                            return;
                        }
                    }
                }
            }
            catch { }

            // Method 2: Using WMI and explorer process (like original script)
            try
            {
                var explorerProcesses = Process.GetProcessesByName("explorer");
                if (explorerProcesses.Length > 0)
                {
                    var explorerProcess = explorerProcesses[0];

                    // This is a simplified version - the original uses complex WMI queries
                    var currentUser = Environment.UserName;
                    var domain = Environment.UserDomainName;
                    var account = new NTAccount(domain, currentUser);
                    var sid = (SecurityIdentifier)account.Translate(typeof(SecurityIdentifier));
                    _sid = sid.Value;

                    AppendOutput($"تم الحصول على SID عبر Explorer: {_sid}");
                    AppendOutput($"Got SID via Explorer: {_sid}");
                }
            }
            catch (Exception ex)
            {
                AppendOutput($"فشل في الحصول على SID: {ex.Message}");
                AppendOutput($"Failed to get SID: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في الحصول على SID: {ex.Message}");
            AppendOutput($"Error getting SID: {ex.Message}");
        }
    }

    private void CheckHKCUsync()
    {
        try
        {
            AppendOutput("فحص تزامن HKCU...");
            AppendOutput("Checking HKCU sync...");

            // Delete test keys first (like original script)
            try
            {
                Registry.CurrentUser.DeleteSubKey("IAS_TEST", false);
            }
            catch { }

            try
            {
                Registry.Users.DeleteSubKey($@"{_sid}\IAS_TEST", false);
            }
            catch { }

            // Create test key in HKCU
            using (var key = Registry.CurrentUser.CreateSubKey("IAS_TEST"))
            {
                // Check if it appears in HKU
                try
                {
                    using (var hkuKey = Registry.Users.OpenSubKey($@"{_sid}\IAS_TEST"))
                    {
                        if (hkuKey != null)
                        {
                            HKCUsync = true;
                            AppendOutput("HKCU متزامن مع HKU");
                            AppendOutput("HKCU is synced with HKU");
                        }
                        else
                        {
                            HKCUsync = false;
                            AppendOutput("HKCU غير متزامن مع HKU");
                            AppendOutput("HKCU is not synced with HKU");
                        }
                    }
                }
                catch
                {
                    HKCUsync = false;
                    AppendOutput("HKCU غير متزامن مع HKU");
                    AppendOutput("HKCU is not synced with HKU");
                }
            }

            // Clean up test keys
            try
            {
                Registry.CurrentUser.DeleteSubKey("IAS_TEST", false);
            }
            catch { }

            try
            {
                Registry.Users.DeleteSubKey($@"{_sid}\IAS_TEST", false);
            }
            catch { }

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في فحص تزامن HKCU: {ex.Message}");
            AppendOutput($"Error checking HKCU sync: {ex.Message}");
            HKCUsync = false;
        }
    }

    private void SetArchitectureAndPaths()
    {
        try
        {
            AppendOutput("تحديد معمارية النظام ومسارات الريجستري...");
            AppendOutput("Setting system architecture and registry paths...");

            // Get architecture (like original script)
            arch = Environment.Is64BitOperatingSystem ? "x64" : "x86";

            if (arch == "x86")
            {
                CLSID = @"HKCU\Software\Classes\CLSID";
                CLSID2 = $@"HKU\{_sid}\Software\Classes\CLSID";
                HKLM = @"HKLM\Software\Internet Download Manager";
            }
            else
            {
                CLSID = @"HKCU\Software\Classes\Wow6432Node\CLSID";
                CLSID2 = $@"HKU\{_sid}\Software\Classes\Wow6432Node\CLSID";
                HKLM = @"HKLM\SOFTWARE\Wow6432Node\Internet Download Manager";
            }

            AppendOutput($"المعمارية: {arch}");
            AppendOutput($"Architecture: {arch}");
            AppendOutput($"CLSID: {CLSID}");
            AppendOutput($"CLSID2: {CLSID2}");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تحديد المعمارية: {ex.Message}");
            AppendOutput($"Error setting architecture: {ex.Message}");
        }
    }

    private void GetIDMPathFromRegistry()
    {
        try
        {
            AppendOutput("الحصول على مسار IDM من الريجستري...");
            AppendOutput("Getting IDM path from registry...");

            // Try to get from registry first (like original script)
            try
            {
                using (var key = Registry.Users.OpenSubKey($@"{_sid}\Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        IDMan = key.GetValue("ExePath")?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(IDMan) && File.Exists(IDMan))
                        {
                            AppendOutput($"تم العثور على IDM في الريجستري: {IDMan}");
                            AppendOutput($"Found IDM in registry: {IDMan}");
                            return;
                        }
                    }
                }
            }
            catch { }

            // Fallback to default paths (like original script)
            if (arch == "x64")
            {
                IDMan = @"C:\Program Files (x86)\Internet Download Manager\IDMan.exe";
            }
            else
            {
                IDMan = @"C:\Program Files\Internet Download Manager\IDMan.exe";
            }

            if (File.Exists(IDMan))
            {
                AppendOutput($"تم العثور على IDM في المسار الافتراضي: {IDMan}");
                AppendOutput($"Found IDM in default path: {IDMan}");
            }
            else
            {
                AppendOutput("لم يتم العثور على IDM");
                AppendOutput("IDM not found");
                IDMan = "";
            }

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في الحصول على مسار IDM: {ex.Message}");
            AppendOutput($"Error getting IDM path: {ex.Message}");
        }
    }

    private void LockIDMKeysAdvanced()
    {
        try
        {
            AppendOutput("قفل المفاتيح بالتقنيات المتقدمة...");
            AppendOutput("Locking keys with advanced techniques...");

            EnableRequiredPrivileges();

            foreach (string keyName in finalValues)
            {
                try
                {
                    // Lock in HKCU
                    LockKeyInPath(CLSID, keyName, "HKCU");

                    // Lock in HKU if not synced
                    if (!HKCUsync)
                    {
                        LockKeyInPath(CLSID2, keyName, "HKU");
                    }

                    AppendOutput($"Locked - {keyName}");
                }
                catch (Exception ex)
                {
                    AppendOutput($"Failed to lock {keyName}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في القفل المتقدم: {ex.Message}");
            AppendOutput($"Error in advanced locking: {ex.Message}");
        }
    }

    private void DeleteIDMKeys()
    {
        try
        {
            AppendOutput("حذف المفاتيح بالتقنيات المتقدمة...");
            AppendOutput("Deleting keys with advanced techniques...");

            EnableRequiredPrivileges();

            foreach (string keyName in finalValues)
            {
                try
                {
                    // Delete from HKCU
                    DeleteKeyFromPath(CLSID, keyName, "HKCU");

                    // Delete from HKU if not synced
                    if (!HKCUsync)
                    {
                        DeleteKeyFromPath(CLSID2, keyName, "HKU");
                    }

                    AppendOutput($"Deleted - {keyName}");
                }
                catch (Exception ex)
                {
                    AppendOutput($"Failed to delete {keyName}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في الحذف المتقدم: {ex.Message}");
            AppendOutput($"Error in advanced deletion: {ex.Message}");
        }
    }

    private void LockKeyInPath(string regPath, string keyName, string rootName)
    {
        try
        {
            RegistryKey rootKey;
            string subPath;

            if (rootName == "HKCU")
            {
                rootKey = Registry.CurrentUser;
                subPath = regPath.Replace("HKCU\\", "") + "\\" + keyName;
            }
            else
            {
                rootKey = Registry.Users;
                subPath = regPath.Replace("HKU\\", "") + "\\" + keyName;
            }

            // Create key if it doesn't exist
            using (var key = rootKey.CreateSubKey(subPath))
            {
                if (key != null)
                {
                    key.SetValue("IAS_Frozen", DateTime.Now.ToString());
                }
            }

            // Apply advanced locking
            TakePermissionsAndLock(rootName, subPath);
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في قفل {keyName}: {ex.Message}");
            AppendOutput($"Error locking {keyName}: {ex.Message}");
        }
    }

    private void DeleteKeyFromPath(string regPath, string keyName, string rootName)
    {
        try
        {
            RegistryKey rootKey;
            string subPath;

            if (rootName == "HKCU")
            {
                rootKey = Registry.CurrentUser;
                subPath = regPath.Replace("HKCU\\", "") + "\\" + keyName;
            }
            else
            {
                rootKey = Registry.Users;
                subPath = regPath.Replace("HKU\\", "") + "\\" + keyName;
            }

            // Try normal deletion first
            try
            {
                rootKey.DeleteSubKeyTree(subPath, false);
                return;
            }
            catch
            {
                // Key might be locked, try advanced deletion
                TakeOwnershipAndDelete(rootName, subPath);
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حذف {keyName}: {ex.Message}");
            AppendOutput($"Error deleting {keyName}: {ex.Message}");
        }
    }

    private void CreateDummyIDMKeysAdvanced()
    {
        try
        {
            AppendOutput("إنشاء مفاتيح وهمية متقدمة...");
            AppendOutput("Creating advanced dummy keys...");

            Random rand = new Random();

            for (int i = 0; i < 5; i++)
            {
                string dummyGuid = "{" + Guid.NewGuid().ToString().ToUpper() + "}";

                try
                {
                    // Create in HKCU
                    CreateDummyKeyInPath(CLSID, dummyGuid, "HKCU", rand);

                    // Create in HKU if not synced
                    if (!HKCUsync)
                    {
                        CreateDummyKeyInPath(CLSID2, dummyGuid, "HKU", rand);
                    }

                    finalValues.Add(dummyGuid);
                    AppendOutput($"Created dummy key: {dummyGuid}");
                }
                catch (Exception ex)
                {
                    AppendOutput($"Failed to create dummy key: {ex.Message}");
                }
            }

            // Lock the dummy keys
            LockIDMKeysAdvanced();
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء المفاتيح الوهمية: {ex.Message}");
            AppendOutput($"Error creating dummy keys: {ex.Message}");
        }
    }

    private void CreateDummyKeyInPath(string regPath, string keyName, string rootName, Random rand)
    {
        try
        {
            RegistryKey rootKey;
            string subPath;

            if (rootName == "HKCU")
            {
                rootKey = Registry.CurrentUser;
                subPath = regPath.Replace("HKCU\\", "") + "\\" + keyName;
            }
            else
            {
                rootKey = Registry.Users;
                subPath = regPath.Replace("HKU\\", "") + "\\" + keyName;
            }

            using (var key = rootKey.CreateSubKey(subPath))
            {
                if (key != null)
                {
                    key.SetValue("", rand.Next(100000, 999999).ToString());
                    key.SetValue("IAS_Frozen", DateTime.Now.ToString());
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء مفتاح وهمي: {ex.Message}");
            AppendOutput($"Error creating dummy key: {ex.Message}");
        }
    }

    private void DeleteExistingIDMKeys()
    {
        try
        {
            AppendOutput("حذف شامل لجميع مفاتيح IDM الموجودة...");
            AppendOutput("Comprehensive deletion of all existing IDM keys...");

            // Step 1: Delete all DownloadManager registry values (like original script)
            DeleteDownloadManagerValues();

            // Step 2: Delete HKLM AdvIntDriverEnabled2 key
            DeleteHKLMActivationKeys();

            // Step 3: Delete CLSID keys using advanced search
            DeleteCLSIDKeys();

            AppendOutput("تم حذف جميع مفاتيح IDM الموجودة بنجاح");
            AppendOutput("Successfully deleted all existing IDM keys");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حذف المفاتيح الموجودة: {ex.Message}");
            AppendOutput($"Error deleting existing keys: {ex.Message}");
        }
    }

    private void DeleteDownloadManagerValues()
    {
        try
        {
            AppendOutput("حذف قيم DownloadManager...");
            AppendOutput("Deleting DownloadManager values...");

            // Values to delete (like original script)
            string[] valuesToDelete = {
                "FName", "LName", "Email", "Serial", "scansk", "tvfrdt",
                "radxcnt", "LstCheck", "ptrk_scdt", "LastCheckQU"
            };

            // Delete from HKCU
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(@"Software\DownloadManager", true))
                {
                    if (key != null)
                    {
                        foreach (string valueName in valuesToDelete)
                        {
                            try
                            {
                                key.DeleteValue(valueName, false);
                                AppendOutput($"Deleted HKCU value: {valueName}");
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Delete from HKU if not synced (like original script)
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.OpenSubKey($@"{_sid}\Software\DownloadManager", true))
                    {
                        if (key != null)
                        {
                            foreach (string valueName in valuesToDelete)
                            {
                                try
                                {
                                    key.DeleteValue(valueName, false);
                                    AppendOutput($"Deleted HKU value: {valueName}");
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حذف قيم DownloadManager: {ex.Message}");
            AppendOutput($"Error deleting DownloadManager values: {ex.Message}");
        }
    }

    private void DeleteHKLMActivationKeys()
    {
        try
        {
            AppendOutput("حذف مفاتيح HKLM...");
            AppendOutput("Deleting HKLM keys...");

            // Delete AdvIntDriverEnabled2 from both paths
            string[] hklmPaths = {
                @"SOFTWARE\Internet Download Manager",
                @"SOFTWARE\WOW6432Node\Internet Download Manager"
            };

            foreach (string path in hklmPaths)
            {
                try
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(path, true))
                    {
                        if (key != null)
                        {
                            key.DeleteValue("AdvIntDriverEnabled2", false);
                            AppendOutput($"Deleted HKLM\\{path}\\AdvIntDriverEnabled2");
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حذف مفاتيح HKLM: {ex.Message}");
            AppendOutput($"Error deleting HKLM keys: {ex.Message}");
        }
    }

    private void DeleteCLSIDKeys()
    {
        try
        {
            AppendOutput("حذف مفاتيح CLSID...");
            AppendOutput("Deleting CLSID keys...");

            if (string.IsNullOrEmpty(CLSID) || string.IsNullOrEmpty(_sid))
            {
                AppendOutput("تحذير: لم يتم تهيئة النظام المتقدم");
                AppendOutput("Warning: Advanced system not initialized");
                return;
            }

            // Clear previous results and search for existing keys
            finalValues.Clear();
            AdvancedCLSIDSearch();

            if (finalValues.Count > 0)
            {
                AppendOutput($"تم العثور على {finalValues.Count} مفتاح CLSID للحذف");
                AppendOutput($"Found {finalValues.Count} CLSID keys to delete");

                // Delete all existing CLSID keys
                DeleteIDMKeys();
            }
            else
            {
                AppendOutput("لا توجد مفاتيح CLSID للحذف");
                AppendOutput("No CLSID keys found to delete");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حذف مفاتيح CLSID: {ex.Message}");
            AppendOutput($"Error deleting CLSID keys: {ex.Message}");
        }
    }

    private void AddMainActivationKey()
    {
        try
        {
            AppendOutput("إضافة مفاتيح التفعيل المتقدمة...");
            AppendOutput("Adding advanced activation keys...");

            // Add AdvIntDriverEnabled2 key (like :add_key in original script)
            AddAdvIntDriverEnabled2();

            // Add additional powerful activation keys
            AddPowerfulActivationKeys();

            AppendOutput("تم إضافة جميع مفاتيح التفعيل المتقدمة");
            AppendOutput("All advanced activation keys added");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة مفاتيح التفعيل: {ex.Message}");
            AppendOutput($"Error adding activation keys: {ex.Message}");
        }
    }

    private void AddAdvIntDriverEnabled2()
    {
        try
        {
            AppendOutput("إضافة مفتاح AdvIntDriverEnabled2...");
            AppendOutput("Adding AdvIntDriverEnabled2 key...");

            string[] hklmPaths = {
                @"SOFTWARE\Internet Download Manager",
                @"SOFTWARE\WOW6432Node\Internet Download Manager"
            };

            foreach (string path in hklmPaths)
            {
                try
                {
                    using (var key = Registry.LocalMachine.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("AdvIntDriverEnabled2", 1, RegistryValueKind.DWord);
                            AppendOutput($"Added - HKLM\\{path}\\AdvIntDriverEnabled2");
                        }
                    }
                }
                catch (UnauthorizedAccessException)
                {
                    AppendOutput($"تحذير: فشل في الوصول لـ HKLM\\{path}");
                    AppendOutput($"Warning: Failed to access HKLM\\{path}");
                }
                catch (Exception ex)
                {
                    AppendOutput($"خطأ في إضافة {path}: {ex.Message}");
                    AppendOutput($"Error adding {path}: {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة AdvIntDriverEnabled2: {ex.Message}");
            AppendOutput($"Error adding AdvIntDriverEnabled2: {ex.Message}");
        }
    }

    private void AddPowerfulActivationKeys()
    {
        try
        {
            AppendOutput("إضافة مفاتيح التفعيل القوية الإضافية...");
            AppendOutput("Adding additional powerful activation keys...");

            // Add powerful registry values to DownloadManager
            AddDownloadManagerActivationValues();

            // Add system-level activation keys
            AddSystemLevelActivationKeys();

            // Add user-level activation keys
            AddUserLevelActivationKeys();
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة المفاتيح القوية: {ex.Message}");
            AppendOutput($"Error adding powerful keys: {ex.Message}");
        }
    }

    private void AddDownloadManagerActivationValues()
    {
        try
        {
            AppendOutput("إضافة قيم التفعيل في DownloadManager...");
            AppendOutput("Adding activation values in DownloadManager...");

            // Powerful activation values
            var activationValues = new Dictionary<string, object>
            {
                {"AdvIntDriverEnabled", 1},
                {"AdvIntDriverEnabled2", 1},
                {"IEMonitorEnabled", 1},
                {"FirefoxMonitorEnabled", 1},
                {"ChromeMonitorEnabled", 1},
                {"OperaMonitorEnabled", 1},
                {"EdgeMonitorEnabled", 1},
                {"SafariMonitorEnabled", 1},
                {"MaxConnections", 32},
                {"MaxConnectionsPerServer", 16},
                {"EnableSpeedLimiter", 0},
                {"CheckForUpdates", 0},
                {"ShowNotifications", 0}
            };

            // Add to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in activationValues)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                                AppendOutput($"Added HKCU value: {kvp.Key} = {kvp.Value}");
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Add to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in activationValues)
                            {
                                try
                                {
                                    if (kvp.Value is int intValue)
                                    {
                                        key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                    }
                                    else
                                    {
                                        key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                    }
                                    AppendOutput($"Added HKU value: {kvp.Key} = {kvp.Value}");
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة قيم DownloadManager: {ex.Message}");
            AppendOutput($"Error adding DownloadManager values: {ex.Message}");
        }
    }

    private void RunCLSIDProcessingScript(bool lockKey, bool deleteKey, bool toggle)
    {
        try
        {
            AppendOutput("تشغيل معالج CLSID المتقدم...");
            AppendOutput("Running advanced CLSID processor...");

            // Clear previous results
            finalValues.Clear();

            // Search for IDM keys
            AdvancedCLSIDSearch();

            if (finalValues.Count > 0)
            {
                AppendOutput($"تم العثور على {finalValues.Count} مفتاح IDM");
                AppendOutput($"Found {finalValues.Count} IDM keys");

                // Apply toggle logic (like original script)
                if (toggle && finalValues.Count > 20)
                {
                    AppendOutput("عدد المفاتيح أكثر من 20، سيتم حذفها بدلاً من قفلها...");
                    AppendOutput("Key count is more than 20, deleting them instead of locking...");
                    DeleteIDMKeys();
                }
                else if (lockKey)
                {
                    AppendOutput("قفل مفاتيح IDM...");
                    AppendOutput("Locking IDM keys...");
                    LockIDMKeysAdvanced();
                }
                else if (deleteKey)
                {
                    AppendOutput("حذف مفاتيح IDM...");
                    AppendOutput("Deleting IDM keys...");
                    DeleteIDMKeys();
                }
            }
            else
            {
                AppendOutput("لم يتم العثور على مفاتيح IDM");
                AppendOutput("No IDM keys found");

                if (lockKey)
                {
                    // Create dummy keys for activation
                    CreateDummyIDMKeysAdvanced();
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في معالج CLSID: {ex.Message}");
            AppendOutput($"Error in CLSID processor: {ex.Message}");
        }
    }

    private void AddSystemLevelActivationKeys()
    {
        try
        {
            AppendOutput("إضافة مفاتيح التفعيل على مستوى النظام...");
            AppendOutput("Adding system-level activation keys...");

            // Add powerful system registry keys
            string[] systemPaths = {
                @"SOFTWARE\Classes\IDMShellExt",
                @"SOFTWARE\Classes\IDMIEHlprObj.IDMIEHlprObj",
                @"SOFTWARE\Classes\IDMIEHlprObj.IDMIEHlprObj.1",
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Browser Helper Objects\{0055C089-8F53-4793-906A-B76802C6CACE}",
                @"SOFTWARE\WOW6432Node\Classes\IDMShellExt",
                @"SOFTWARE\WOW6432Node\Classes\IDMIEHlprObj.IDMIEHlprObj",
                @"SOFTWARE\WOW6432Node\Classes\IDMIEHlprObj.IDMIEHlprObj.1",
                @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Explorer\Browser Helper Objects\{0055C089-8F53-4793-906A-B76802C6CACE}"
            };

            foreach (string path in systemPaths)
            {
                try
                {
                    using (var key = Registry.LocalMachine.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("", "IDM Integration", RegistryValueKind.String);
                            key.SetValue("Enabled", 1, RegistryValueKind.DWord);
                            AppendOutput($"Added system key: {path}");
                        }
                    }
                }
                catch
                {
                    AppendOutput($"تحذير: فشل في إضافة {path}");
                    AppendOutput($"Warning: Failed to add {path}");
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة مفاتيح النظام: {ex.Message}");
            AppendOutput($"Error adding system keys: {ex.Message}");
        }
    }

    private void AddUserLevelActivationKeys()
    {
        try
        {
            AppendOutput("إضافة مفاتيح التفعيل على مستوى المستخدم...");
            AppendOutput("Adding user-level activation keys...");

            // Add powerful user registry keys
            string[] userPaths = {
                @"Software\Classes\IDMShellExt",
                @"Software\Classes\IDMIEHlprObj.IDMIEHlprObj",
                @"Software\Classes\IDMIEHlprObj.IDMIEHlprObj.1",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download all links with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download FLV video with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download selected links with IDM"
            };

            // Add to HKCU
            foreach (string path in userPaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("", "IDM Integration", RegistryValueKind.String);
                            key.SetValue("Enabled", 1, RegistryValueKind.DWord);
                            AppendOutput($"Added HKCU key: {path}");
                        }
                    }
                }
                catch { }
            }

            // Add to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                foreach (string path in userPaths)
                {
                    try
                    {
                        using (var key = Registry.Users.CreateSubKey($@"{_sid}\{path}"))
                        {
                            if (key != null)
                            {
                                key.SetValue("", "IDM Integration", RegistryValueKind.String);
                                key.SetValue("Enabled", 1, RegistryValueKind.DWord);
                                AppendOutput($"Added HKU key: {path}");
                            }
                        }
                    }
                    catch { }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إضافة مفاتيح المستخدم: {ex.Message}");
            AppendOutput($"Error adding user keys: {ex.Message}");
        }
    }

    private void RegisterIDMWithFakeSerial()
    {
        try
        {
            AppendOutput("تطبيق بيانات التسجيل المزيفة المتقدمة...");
            AppendOutput("Applying advanced fake registration details...");

            // Generate super strong fake registration data
            Random rand = new Random();
            string fname = GenerateRandomName(rand);
            string lname = GenerateRandomName(rand);
            string email = $"{fname.ToLower()}.{lname.ToLower()}@tonec.com";

            // Generate multiple fake serials for maximum strength
            string serial1 = GenerateFakeSerial();
            string serial2 = GenerateFakeSerial();
            string serial3 = GenerateFakeSerial();

            AppendOutput($"الاسم المزيف: {fname} {lname}");
            AppendOutput($"Fake Name: {fname} {lname}");
            AppendOutput($"الإيميل المزيف: {email}");
            AppendOutput($"Fake Email: {email}");
            AppendOutput($"السيريال الأساسي: {serial1}");
            AppendOutput($"Primary Serial: {serial1}");

            // Apply powerful registration data
            ApplyPowerfulRegistrationData(fname, lname, email, serial1, serial2, serial3, rand);

            AppendOutput("تم تطبيق بيانات التسجيل المتقدمة بنجاح");
            AppendOutput("Advanced registration data applied successfully");

        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق بيانات التسجيل: {ex.Message}");
            AppendOutput($"Error applying registration data: {ex.Message}");
        }
    }

    private string GenerateRandomName(Random rand)
    {
        string[] firstNames = { "Ahmed", "Mohamed", "Ali", "Omar", "Hassan", "Mahmoud", "Youssef", "Khaled", "Amr", "Tamer" };
        string[] lastNames = { "Ibrahim", "Hassan", "Mohamed", "Ali", "Mahmoud", "Ahmed", "Omar", "Youssef", "Khaled", "Amr" };

        return rand.Next(2) == 0 ?
            firstNames[rand.Next(firstNames.Length)] :
            $"{firstNames[rand.Next(firstNames.Length)]}{rand.Next(100, 999)}";
    }

    private void ApplyPowerfulRegistrationData(string fname, string lname, string email, string serial1, string serial2, string serial3, Random rand)
    {
        try
        {
            // Comprehensive registration values
            var registrationData = new Dictionary<string, object>
            {
                {"FName", fname},
                {"LName", lname},
                {"Email", email},
                {"Serial", serial1},
                {"Serial2", serial2},
                {"Serial3", serial3},
                {"scansk", GenerateRandomHex(rand, 32)},
                {"tvfrdt", GenerateRandomHex(rand, 16)},
                {"radxcnt", rand.Next(1000, 9999)},
                {"LstCheck", DateTime.Now.AddDays(-rand.Next(1, 30)).ToString("yyyy-MM-dd")},
                {"ptrk_scdt", GenerateRandomHex(rand, 24)},
                {"LastCheckQU", DateTime.Now.AddDays(-rand.Next(1, 15)).ToString("yyyy-MM-dd HH:mm:ss")},
                {"RegName", $"{fname} {lname}"},
                {"RegEmail", email},
                {"RegSerial", serial1},
                {"ActivationDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")},
                {"ActivationStatus", "Activated"},
                {"LicenseType", "Lifetime"},
                {"ProductVersion", "6.41.11"},
                {"BuildNumber", "6"},
                {"RegistrationKey", GenerateRandomHex(rand, 64)},
                {"ValidationCode", GenerateRandomHex(rand, 32)},
                {"AuthToken", GenerateRandomHex(rand, 48)}
            };

            // Apply to HKCU
            ApplyRegistrationToRegistry(Registry.CurrentUser, @"Software\DownloadManager", registrationData, "HKCU");

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                ApplyRegistrationToRegistry(Registry.Users, $@"{_sid}\Software\DownloadManager", registrationData, "HKU");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق البيانات القوية: {ex.Message}");
            AppendOutput($"Error applying powerful data: {ex.Message}");
        }
    }

    private string GenerateRandomHex(Random rand, int length)
    {
        const string hexChars = "0123456789ABCDEF";
        return new string(Enumerable.Repeat(hexChars, length)
            .Select(s => s[rand.Next(s.Length)]).ToArray());
    }

    private void ApplyRegistrationToRegistry(RegistryKey rootKey, string path, Dictionary<string, object> data, string rootName)
    {
        try
        {
            using (var key = rootKey.CreateSubKey(path))
            {
                if (key != null)
                {
                    foreach (var kvp in data)
                    {
                        try
                        {
                            if (kvp.Value is int intValue)
                            {
                                key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                            }
                            else
                            {
                                key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                            }
                            AppendOutput($"Applied {rootName} registration: {kvp.Key}");
                        }
                        catch { }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق بيانات {rootName}: {ex.Message}");
            AppendOutput($"Error applying {rootName} data: {ex.Message}");
        }
    }

    private void ApplyUltraPowerfulProtection()
    {
        try
        {
            AppendOutput("تطبيق تقنيات الحماية الفائقة...");
            AppendOutput("Applying ultra-powerful protection techniques...");

            // Create hidden activation markers
            CreateHiddenActivationMarkers();

            // Apply registry protection
            ApplyRegistryProtection();

            // Create system-level activation locks
            CreateSystemLevelLocks();

            AppendOutput("تم تطبيق الحماية الفائقة بنجاح");
            AppendOutput("Ultra-powerful protection applied successfully");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق الحماية الفائقة: {ex.Message}");
            AppendOutput($"Error applying ultra protection: {ex.Message}");
        }
    }

    private void CreateHiddenActivationMarkers()
    {
        try
        {
            Random rand = new Random();

            // Create hidden markers in multiple locations
            string[] hiddenPaths = {
                @"Software\Microsoft\Windows\CurrentVersion\Explorer\Advanced",
                @"Software\Microsoft\Windows\CurrentVersion\Policies\Explorer",
                @"Software\Microsoft\Internet Explorer\Main",
                @"Software\Microsoft\Windows\CurrentVersion\Internet Settings"
            };

            foreach (string path in hiddenPaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            // Hidden activation markers
                            key.SetValue($"IDM_Marker_{rand.Next(1000, 9999)}", GenerateRandomHex(rand, 16), RegistryValueKind.String);
                            key.SetValue($"Activation_Token_{rand.Next(1000, 9999)}", DateTime.Now.ToBinary().ToString(), RegistryValueKind.String);
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم إنشاء علامات التفعيل المخفية");
            AppendOutput("Hidden activation markers created");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء العلامات المخفية: {ex.Message}");
            AppendOutput($"Error creating hidden markers: {ex.Message}");
        }
    }

    private void ApplyRegistryProtection()
    {
        try
        {
            AppendOutput("تطبيق حماية الريجستري...");
            AppendOutput("Applying registry protection...");

            // Create multiple backup locations for activation data
            string[] backupPaths = {
                @"Software\Classes\.idm",
                @"Software\Classes\.idmx",
                @"Software\Classes\MIME\Database\Content Type\application/x-idm",
                @"Software\RegisteredApplications"
            };

            Random rand = new Random();
            foreach (string path in backupPaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("IDM_Backup_Data", GenerateRandomHex(rand, 32), RegistryValueKind.String);
                            key.SetValue("Protection_Level", "Maximum", RegistryValueKind.String);
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم تطبيق حماية الريجستري");
            AppendOutput("Registry protection applied");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في حماية الريجستري: {ex.Message}");
            AppendOutput($"Error in registry protection: {ex.Message}");
        }
    }

    private void CreateSystemLevelLocks()
    {
        try
        {
            AppendOutput("إنشاء أقفال مستوى النظام...");
            AppendOutput("Creating system-level locks...");

            // Create system-level activation locks
            try
            {
                string[] systemLockPaths = {
                    @"SOFTWARE\Classes\Applications\IDMan.exe",
                    @"SOFTWARE\Classes\Applications\IDMan.exe\shell\open\command",
                    @"SOFTWARE\WOW6432Node\Classes\Applications\IDMan.exe",
                    @"SOFTWARE\WOW6432Node\Classes\Applications\IDMan.exe\shell\open\command"
                };

                foreach (string path in systemLockPaths)
                {
                    try
                    {
                        using (var key = Registry.LocalMachine.CreateSubKey(path))
                        {
                            if (key != null)
                            {
                                key.SetValue("IDM_System_Lock", "Activated", RegistryValueKind.String);
                                key.SetValue("Protection_Enabled", 1, RegistryValueKind.DWord);
                            }
                        }
                    }
                    catch { }
                }
            }
            catch { }

            AppendOutput("تم إنشاء أقفال مستوى النظام");
            AppendOutput("System-level locks created");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء الأقفال: {ex.Message}");
            AppendOutput($"Error creating locks: {ex.Message}");
        }
    }

    private void CreateMultipleActivationBackups()
    {
        try
        {
            AppendOutput("إنشاء نسخ احتياطية متعددة للتفعيل...");
            AppendOutput("Creating multiple activation backups...");

            Random rand = new Random();

            // Create activation backups in multiple registry locations
            for (int i = 1; i <= 5; i++)
            {
                try
                {
                    string backupPath = $@"Software\IDM_Backup_{i}_{rand.Next(1000, 9999)}";

                    using (var key = Registry.CurrentUser.CreateSubKey(backupPath))
                    {
                        if (key != null)
                        {
                            // Store activation backup data
                            key.SetValue("BackupSerial", GenerateFakeSerial(), RegistryValueKind.String);
                            key.SetValue("BackupKey", GenerateRandomHex(rand, 64), RegistryValueKind.String);
                            key.SetValue("BackupDate", DateTime.Now.ToString(), RegistryValueKind.String);
                            key.SetValue("BackupLevel", i, RegistryValueKind.DWord);
                            key.SetValue("ActivationStatus", "Permanent", RegistryValueKind.String);
                        }
                    }

                    AppendOutput($"تم إنشاء النسخة الاحتياطية رقم {i}");
                    AppendOutput($"Created backup #{i}");
                }
                catch { }
            }

            AppendOutput("تم إنشاء جميع النسخ الاحتياطية");
            AppendOutput("All activation backups created");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إنشاء النسخ الاحتياطية: {ex.Message}");
            AppendOutput($"Error creating backups: {ex.Message}");
        }
    }

    private void ApplyStealthActivationTechniques()
    {
        try
        {
            AppendOutput("تطبيق تقنيات التفعيل الخفية...");
            AppendOutput("Applying stealth activation techniques...");

            // Apply stealth techniques to hide activation traces
            ApplyStealthRegistryEntries();

            // Create decoy entries
            CreateDecoyEntries();

            // Apply time-based activation locks
            ApplyTimeBasedLocks();

            AppendOutput("تم تطبيق تقنيات التفعيل الخفية");
            AppendOutput("Stealth activation techniques applied");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تطبيق التقنيات الخفية: {ex.Message}");
            AppendOutput($"Error applying stealth techniques: {ex.Message}");
        }
    }

    private void ApplyStealthRegistryEntries()
    {
        try
        {
            Random rand = new Random();

            // Create stealth entries that look like normal Windows entries
            string[] stealthPaths = {
                @"Software\Microsoft\Windows\CurrentVersion\App Paths\IDMan.exe",
                @"Software\Microsoft\Windows\CurrentVersion\Uninstall\Internet Download Manager",
                @"Software\Classes\IDMShellExt\CLSID",
                @"Software\Classes\TypeLib\{F1B9284F-E9DC-4e68-9D7E-42362A59F0FD}"
            };

            foreach (string path in stealthPaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            // Stealth activation data disguised as normal entries
                            key.SetValue("", IDMan ?? @"C:\Program Files\Internet Download Manager\IDMan.exe", RegistryValueKind.String);
                            key.SetValue("Version", "6.41.11", RegistryValueKind.String);
                            key.SetValue("Publisher", "Tonec Inc.", RegistryValueKind.String);
                            key.SetValue("Stealth_Data", GenerateRandomHex(rand, 24), RegistryValueKind.String);
                        }
                    }
                }
                catch { }
            }
        }
        catch { }
    }

    private void CreateDecoyEntries()
    {
        try
        {
            Random rand = new Random();

            // Create decoy entries to confuse detection systems
            for (int i = 1; i <= 3; i++)
            {
                try
                {
                    string decoyPath = $@"Software\Decoy_App_{rand.Next(1000, 9999)}";

                    using (var key = Registry.CurrentUser.CreateSubKey(decoyPath))
                    {
                        if (key != null)
                        {
                            key.SetValue("AppName", $"System Component {i}", RegistryValueKind.String);
                            key.SetValue("Version", $"1.{rand.Next(0, 9)}.{rand.Next(0, 99)}", RegistryValueKind.String);
                            key.SetValue("Data", GenerateRandomHex(rand, 16), RegistryValueKind.String);
                        }
                    }
                }
                catch { }
            }
        }
        catch { }
    }

    private void ApplyTimeBasedLocks()
    {
        try
        {
            // Create time-based activation locks
            using (var key = Registry.CurrentUser.CreateSubKey(@"Software\Microsoft\Windows\CurrentVersion\Explorer\UserAssist\{CEBFF5CD-ACE2-4F4F-9178-9926F41749EA}\Count"))
            {
                if (key != null)
                {
                    // Time-based lock using Windows UserAssist
                    string lockKey = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("IDM_Time_Lock"));
                    key.SetValue(lockKey, DateTime.Now.ToBinary(), RegistryValueKind.QWord);
                }
            }
        }
        catch { }
    }

    private void CreateCriticalRegistryBackup()
    {
        try
        {
            AppendOutput("إنشاء نسخة احتياطية حاسمة من مفاتيح CLSID...");
            AppendOutput("Creating critical backup of CLSID registry keys...");

            // Generate timestamp like original script: yyyyMMdd-HHmmssfff
            string timestamp = DateTime.Now.ToString("yyyyMMdd-HHmmssfff");
            string tempPath = Environment.GetEnvironmentVariable("SystemRoot") + @"\Temp";

            // Ensure temp directory exists
            if (!Directory.Exists(tempPath))
            {
                Directory.CreateDirectory(tempPath);
            }

            AppendOutput($"إنشاء نسخة احتياطية في {tempPath}");
            AppendOutput($"Creating backup in {tempPath}");

            // Backup HKCU CLSID (like original script)
            string backupFile1 = Path.Combine(tempPath, $"_Backup_HKCU_CLSID_{timestamp}.reg");
            ExportRegistryKeyCritical("HKEY_CURRENT_USER\\" + CLSID.Replace("HKCU\\", ""), backupFile1);

            // Backup HKU CLSID if not synced (like original script)
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                string backupFile2 = Path.Combine(tempPath, $"_Backup_HKU-{_sid}_CLSID_{timestamp}.reg");
                ExportRegistryKeyCritical("HKEY_USERS\\" + CLSID2.Replace("HKU\\", ""), backupFile2);
            }

            AppendOutput("تم إنشاء النسخة الاحتياطية الحاسمة بنجاح");
            AppendOutput("Critical backup created successfully");
        }
        catch (Exception ex)
        {
            AppendOutput($"تحذير: فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            AppendOutput($"Warning: Failed to create backup: {ex.Message}");
        }
    }

    private void ExportRegistryKeyCritical(string keyPath, string filePath)
    {
        try
        {
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = "reg",
                    Arguments = $"export \"{keyPath}\" \"{filePath}\"",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    Verb = "runas" // Run as administrator
                }
            };

            process.Start();
            bool finished = process.WaitForExit(15000); // 15 seconds timeout

            if (finished && process.ExitCode == 0)
            {
                AppendOutput($"تم تصدير: {Path.GetFileName(filePath)}");
                AppendOutput($"Exported: {Path.GetFileName(filePath)}");
            }
            else
            {
                AppendOutput($"فشل في تصدير: {Path.GetFileName(filePath)}");
                AppendOutput($"Failed to export: {Path.GetFileName(filePath)}");
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تصدير {keyPath}: {ex.Message}");
            AppendOutput($"Error exporting {keyPath}: {ex.Message}");
        }
    }

    private void FixIDMError0x800706BE()
    {
        try
        {
            AppendOutput("إصلاح خطأ IDM Error 0x800706BE...");
            AppendOutput("Fixing IDM Error 0x800706BE...");

            // This error occurs when IDM can't communicate with browsers
            // Fix browser integration registry keys
            FixBrowserIntegrationKeys();

            // Fix IDM COM registration
            FixIDMCOMRegistration();

            // Fix IDM shell extension
            FixIDMShellExtension();

            // Reset IDM configuration
            ResetIDMConfiguration();

            AppendOutput("تم إصلاح خطأ 0x800706BE بنجاح");
            AppendOutput("Error 0x800706BE fixed successfully");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إصلاح 0x800706BE: {ex.Message}");
            AppendOutput($"Error fixing 0x800706BE: {ex.Message}");
        }
    }

    private void FixBrowserIntegrationKeys()
    {
        try
        {
            AppendOutput("إصلاح مفاتيح تكامل المتصفحات...");
            AppendOutput("Fixing browser integration keys...");

            // Fix Internet Explorer integration
            string[] iePaths = {
                @"Software\Microsoft\Internet Explorer\MenuExt\Download with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download all links with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download FLV video with IDM",
                @"Software\Microsoft\Internet Explorer\MenuExt\Download selected links with IDM"
            };

            foreach (string path in iePaths)
            {
                try
                {
                    using (var key = Registry.CurrentUser.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("", IDMan ?? @"C:\Program Files\Internet Download Manager\IDMan.exe", RegistryValueKind.String);
                            key.SetValue("contexts", 0x22, RegistryValueKind.DWord);
                        }
                    }
                }
                catch { }
            }

            // Fix Browser Helper Object
            try
            {
                using (var key = Registry.LocalMachine.CreateSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Explorer\Browser Helper Objects\{0055C089-8F53-4793-906A-B76802C6CACE}"))
                {
                    if (key != null)
                    {
                        key.SetValue("", "IDM integration (IDMIEHlprObj Class)", RegistryValueKind.String);
                        key.SetValue("NoExplorer", 1, RegistryValueKind.DWord);
                    }
                }
            }
            catch { }

            AppendOutput("تم إصلاح مفاتيح تكامل المتصفحات");
            AppendOutput("Browser integration keys fixed");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إصلاح مفاتيح المتصفحات: {ex.Message}");
            AppendOutput($"Error fixing browser keys: {ex.Message}");
        }
    }

    private void FixIDMCOMRegistration()
    {
        try
        {
            AppendOutput("إصلاح تسجيل IDM COM...");
            AppendOutput("Fixing IDM COM registration...");

            if (string.IsNullOrEmpty(IDMan) || !File.Exists(IDMan))
            {
                AppendOutput("تحذير: لم يتم العثور على IDMan.exe");
                AppendOutput("Warning: IDMan.exe not found");
                return;
            }

            // Re-register IDM COM components
            var process = new Process
            {
                StartInfo = new ProcessStartInfo
                {
                    FileName = IDMan,
                    Arguments = "/regserver",
                    UseShellExecute = false,
                    CreateNoWindow = true,
                    Verb = "runas"
                }
            };

            process.Start();
            process.WaitForExit(10000);

            AppendOutput("تم إعادة تسجيل مكونات IDM COM");
            AppendOutput("IDM COM components re-registered");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تسجيل COM: {ex.Message}");
            AppendOutput($"Error registering COM: {ex.Message}");
        }
    }

    private void FixIDMShellExtension()
    {
        try
        {
            AppendOutput("إصلاح IDM Shell Extension...");
            AppendOutput("Fixing IDM Shell Extension...");

            // Fix shell extension registry keys
            string[] shellPaths = {
                @"SOFTWARE\Classes\*\shellex\ContextMenuHandlers\IDM Shell Extension",
                @"SOFTWARE\Classes\Folder\shellex\ContextMenuHandlers\IDM Shell Extension",
                @"SOFTWARE\Classes\Directory\shellex\ContextMenuHandlers\IDM Shell Extension"
            };

            foreach (string path in shellPaths)
            {
                try
                {
                    using (var key = Registry.LocalMachine.CreateSubKey(path))
                    {
                        if (key != null)
                        {
                            key.SetValue("", "{0055C089-8F53-4793-906A-B76802C6CACE}", RegistryValueKind.String);
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم إصلاح IDM Shell Extension");
            AppendOutput("IDM Shell Extension fixed");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إصلاح Shell Extension: {ex.Message}");
            AppendOutput($"Error fixing Shell Extension: {ex.Message}");
        }
    }

    private void ResetIDMConfiguration()
    {
        try
        {
            AppendOutput("إعادة تعيين إعدادات IDM...");
            AppendOutput("Resetting IDM configuration...");

            // Reset problematic IDM settings that cause 0x800706BE
            var resetValues = new Dictionary<string, object>
            {
                {"IEMonitorEnabled", 1},
                {"FirefoxMonitorEnabled", 1},
                {"ChromeMonitorEnabled", 1},
                {"OperaMonitorEnabled", 1},
                {"EdgeMonitorEnabled", 1},
                {"SafariMonitorEnabled", 1},
                {"AdvIntDriverEnabled", 1},
                {"AdvIntDriverEnabled2", 1},
                {"StartupType", 2},
                {"ShowAddDialog", 1},
                {"CheckAssociation", 0},
                {"CheckUpdtMnu", 0},
                {"OnlyOnce", 0}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in resetValues)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            AppendOutput("تم إعادة تعيين إعدادات IDM");
            AppendOutput("IDM configuration reset");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تعيين الإعدادات: {ex.Message}");
            AppendOutput($"Error resetting configuration: {ex.Message}");
        }
    }

    private void ReRegisterIDMComponents()
    {
        try
        {
            AppendOutput("إعادة تسجيل جميع مكونات IDM...");
            AppendOutput("Re-registering all IDM components...");

            if (string.IsNullOrEmpty(IDMan) || !File.Exists(IDMan))
            {
                AppendOutput("تحذير: لم يتم العثور على IDMan.exe");
                AppendOutput("Warning: IDMan.exe not found");
                return;
            }

            string idmPath = Path.GetDirectoryName(IDMan);

            // Re-register all IDM DLL files
            string[] dllFiles = {
                "IEModule.dll",
                "IDMShellExt.dll",
                "IDMShellExt64.dll",
                "idmmkb.dll",
                "idmmkb64.dll"
            };

            foreach (string dllFile in dllFiles)
            {
                string dllPath = Path.Combine(idmPath, dllFile);
                if (File.Exists(dllPath))
                {
                    try
                    {
                        var process = new Process
                        {
                            StartInfo = new ProcessStartInfo
                            {
                                FileName = "regsvr32",
                                Arguments = $"/s \"{dllPath}\"",
                                UseShellExecute = false,
                                CreateNoWindow = true,
                                Verb = "runas"
                            }
                        };

                        process.Start();
                        process.WaitForExit(5000);

                        AppendOutput($"تم تسجيل: {dllFile}");
                        AppendOutput($"Registered: {dllFile}");
                    }
                    catch { }
                }
            }

            AppendOutput("تم إعادة تسجيل جميع مكونات IDM");
            AppendOutput("All IDM components re-registered");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تسجيل المكونات: {ex.Message}");
            AppendOutput($"Error re-registering components: {ex.Message}");
        }
    }

    private void FixRegistrationMessage()
    {
        try
        {
            AppendOutput("إصلاح رسالة 'لم يسجل منذ 15 يوما'...");
            AppendOutput("Fixing 'Not registered for 15 days' message...");

            // This message appears when IDM thinks it's in trial mode
            // We need to completely remove trial tracking
            RemoveTrialTracking();

            // Set fake registration data to prevent the message
            SetFakeRegistrationData();

            // Reset trial counters completely
            ResetTrialCounters();

            // Remove trial expiration checks
            RemoveTrialExpirationChecks();

            AppendOutput("تم إصلاح رسالة التسجيل بنجاح");
            AppendOutput("Registration message fixed successfully");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إصلاح رسالة التسجيل: {ex.Message}");
            AppendOutput($"Error fixing registration message: {ex.Message}");
        }
    }

    private void RemoveTrialTracking()
    {
        try
        {
            AppendOutput("إزالة تتبع فترة التجربة...");
            AppendOutput("Removing trial tracking...");

            // Values that track trial period - DELETE them completely
            string[] trialTrackingValues = {
                "LstCheck", "LastCheckQU", "ptrk_scdt", "tvfrdt", "radxcnt",
                "InstallDate", "FirstRun", "TrialExpired", "DaysLeft",
                "TrialMode", "TrialDays", "TrialStartDate", "TrialEndDate",
                "CheckUpdtMnu", "CheckAssociation", "OnlyOnce", "RegCheck",
                "LastRegCheck", "TrialCounter", "UsageDays", "RunCount"
            };

            // Remove from HKCU
            try
            {
                using (var key = Registry.CurrentUser.OpenSubKey(@"Software\DownloadManager", true))
                {
                    if (key != null)
                    {
                        foreach (string valueName in trialTrackingValues)
                        {
                            try
                            {
                                key.DeleteValue(valueName, false);
                                AppendOutput($"حذف قيمة التتبع: {valueName}");
                                AppendOutput($"Deleted tracking value: {valueName}");
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Remove from HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.OpenSubKey($@"{_sid}\Software\DownloadManager", true))
                    {
                        if (key != null)
                        {
                            foreach (string valueName in trialTrackingValues)
                            {
                                try
                                {
                                    key.DeleteValue(valueName, false);
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم إزالة جميع قيم تتبع التجربة");
            AppendOutput("All trial tracking values removed");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة تتبع التجربة: {ex.Message}");
            AppendOutput($"Error removing trial tracking: {ex.Message}");
        }
    }

    private void SetFakeRegistrationData()
    {
        try
        {
            AppendOutput("تعيين بيانات تسجيل مزيفة...");
            AppendOutput("Setting fake registration data...");

            Random rand = new Random();

            // Strong fake registration data to prevent "not registered" message
            var fakeRegData = new Dictionary<string, object>
            {
                {"FName", "Mohamed"},
                {"LName", "Issa"},
                {"Email", "<EMAIL>"},
                {"Serial", GenerateFakeSerial()},
                {"RegName", "Mohamed Issa"},
                {"RegEmail", "<EMAIL>"},
                {"RegSerial", GenerateFakeSerial()},
                {"ActivationDate", DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd HH:mm:ss")},
                {"ActivationStatus", "Registered"},
                {"LicenseType", "Lifetime"},
                {"ProductVersion", "6.41.11"},
                {"BuildNumber", "6"},
                {"RegistrationKey", GenerateRandomHex(rand, 64)},
                {"ValidationCode", GenerateRandomHex(rand, 32)},
                {"AuthToken", GenerateRandomHex(rand, 48)},
                {"Registered", 1},
                {"Licensed", 1},
                {"Activated", 1},
                {"ValidLicense", 1}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in fakeRegData)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in fakeRegData)
                            {
                                try
                                {
                                    if (kvp.Value is int intValue)
                                    {
                                        key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                    }
                                    else
                                    {
                                        key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                    }
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم تعيين بيانات التسجيل المزيفة");
            AppendOutput("Fake registration data set");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تعيين بيانات التسجيل: {ex.Message}");
            AppendOutput($"Error setting registration data: {ex.Message}");
        }
    }

    private void ResetTrialCounters()
    {
        try
        {
            AppendOutput("إعادة تعيين عدادات التجربة...");
            AppendOutput("Resetting trial counters...");

            // Reset all trial-related counters to prevent expiration
            var resetCounters = new Dictionary<string, object>
            {
                {"TrialDays", 9999},
                {"DaysLeft", 9999},
                {"TrialExpired", 0},
                {"TrialMode", 0},
                {"FirstRun", 0},
                {"RunCount", 1},
                {"UsageDays", 1},
                {"TrialCounter", 0},
                {"radxcnt", 0}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in resetCounters)
                        {
                            try
                            {
                                key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in resetCounters)
                            {
                                try
                                {
                                    key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم إعادة تعيين عدادات التجربة");
            AppendOutput("Trial counters reset");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إعادة تعيين العدادات: {ex.Message}");
            AppendOutput($"Error resetting counters: {ex.Message}");
        }
    }

    private void RemoveTrialExpirationChecks()
    {
        try
        {
            AppendOutput("إزالة فحوصات انتهاء التجربة...");
            AppendOutput("Removing trial expiration checks...");

            // Disable all checks that could trigger trial expiration
            var disableChecks = new Dictionary<string, object>
            {
                {"CheckUpdtMnu", 0},
                {"CheckAssociation", 0},
                {"OnlyOnce", 0},
                {"RegCheck", 0},
                {"LastRegCheck", "2099-12-31"},
                {"ShowNotifications", 0},
                {"EnableSpeedLimiter", 0},
                {"CheckForUpdates", 0},
                {"AutoUpdate", 0},
                {"UpdateCheck", 0}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in disableChecks)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in disableChecks)
                            {
                                try
                                {
                                    if (kvp.Value is int intValue)
                                    {
                                        key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                    }
                                    else
                                    {
                                        key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                    }
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }

            AppendOutput("تم إزالة فحوصات انتهاء التجربة");
            AppendOutput("Trial expiration checks removed");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إزالة الفحوصات: {ex.Message}");
            AppendOutput($"Error removing checks: {ex.Message}");
        }
    }

    private void ForceDisableRegistrationDialog()
    {
        try
        {
            AppendOutput("إجبار إيقاف نافذة التسجيل...");
            AppendOutput("Force disabling registration dialog...");

            // Disable registration dialog completely
            DisableRegistrationDialogRegistry();

            // Set registration bypass flags
            SetRegistrationBypassFlags();

            // Force registration status
            ForceRegistrationStatus();

            AppendOutput("تم إيقاف نافذة التسجيل بالقوة");
            AppendOutput("Registration dialog forcefully disabled");
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في إيقاف نافذة التسجيل: {ex.Message}");
            AppendOutput($"Error disabling registration dialog: {ex.Message}");
        }
    }

    private void DisableRegistrationDialogRegistry()
    {
        try
        {
            // Registry values that control registration dialog
            var disableDialogValues = new Dictionary<string, object>
            {
                {"ShowRegDialog", 0},
                {"RegDialogShown", 1},
                {"RegistrationRequired", 0},
                {"ShowRegistration", 0},
                {"RegNagShown", 1},
                {"RegNagDisabled", 1},
                {"TrialNagDisabled", 1},
                {"ShowTrialDialog", 0},
                {"ShowNagScreen", 0},
                {"NagScreenDisabled", 1},
                {"RegCheckDisabled", 1},
                {"SkipRegistration", 1},
                {"BypassRegistration", 1}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in disableDialogValues)
                        {
                            try
                            {
                                key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                                AppendOutput($"تم تعطيل: {kvp.Key}");
                                AppendOutput($"Disabled: {kvp.Key}");
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in disableDialogValues)
                            {
                                try
                                {
                                    key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تعطيل نافذة التسجيل: {ex.Message}");
            AppendOutput($"Error disabling registration dialog: {ex.Message}");
        }
    }

    private void SetRegistrationBypassFlags()
    {
        try
        {
            // Set flags to bypass all registration checks
            var bypassFlags = new Dictionary<string, object>
            {
                {"RegBypass", 1},
                {"TrialBypass", 1},
                {"NagBypass", 1},
                {"CheckBypass", 1},
                {"DialogBypass", 1},
                {"RegistrationComplete", 1},
                {"TrialComplete", 1},
                {"LicenseValid", 1},
                {"ProductActivated", 1},
                {"FullVersion", 1}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in bypassFlags)
                        {
                            try
                            {
                                key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in bypassFlags)
                            {
                                try
                                {
                                    key.SetValue(kvp.Key, kvp.Value, RegistryValueKind.DWord);
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في تعيين علامات التجاوز: {ex.Message}");
            AppendOutput($"Error setting bypass flags: {ex.Message}");
        }
    }

    private void ForceRegistrationStatus()
    {
        try
        {
            // Force IDM to think it's registered
            var forceRegValues = new Dictionary<string, object>
            {
                {"IsRegistered", 1},
                {"IsActivated", 1},
                {"IsLicensed", 1},
                {"IsTrial", 0},
                {"IsDemo", 0},
                {"RegStatus", "Registered"},
                {"LicenseStatus", "Valid"},
                {"ActivationStatus", "Activated"},
                {"ProductStatus", "Full"},
                {"VersionStatus", "Licensed"}
            };

            // Apply to HKCU
            try
            {
                using (var key = Registry.CurrentUser.CreateSubKey(@"Software\DownloadManager"))
                {
                    if (key != null)
                    {
                        foreach (var kvp in forceRegValues)
                        {
                            try
                            {
                                if (kvp.Value is int intValue)
                                {
                                    key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                }
                                else
                                {
                                    key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                }
                            }
                            catch { }
                        }
                    }
                }
            }
            catch { }

            // Apply to HKU if not synced
            if (!HKCUsync && !string.IsNullOrEmpty(_sid))
            {
                try
                {
                    using (var key = Registry.Users.CreateSubKey($@"{_sid}\Software\DownloadManager"))
                    {
                        if (key != null)
                        {
                            foreach (var kvp in forceRegValues)
                            {
                                try
                                {
                                    if (kvp.Value is int intValue)
                                    {
                                        key.SetValue(kvp.Key, intValue, RegistryValueKind.DWord);
                                    }
                                    else
                                    {
                                        key.SetValue(kvp.Key, kvp.Value.ToString(), RegistryValueKind.String);
                                    }
                                }
                                catch { }
                            }
                        }
                    }
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            AppendOutput($"خطأ في فرض حالة التسجيل: {ex.Message}");
            AppendOutput($"Error forcing registration status: {ex.Message}");
        }
    }
}
