===============================================
    إصلاح رسالة "لم يسجل منذ 15 يوما"
    Fix "Not Registered for 15 Days" Message
===============================================

🔧 تطوير: المهندس محمد عيسى - 01009046911
   قناة دارك سايبر اكس على يوتيوب - DarkCyberX

===============================================
    🚨 تم إصلاح رسالة "لم يسجل منذ 15 يوما" بنجاح!
    "Not Registered for 15 Days" Message Successfully Fixed!
===============================================

### 📋 **تشخيص الرسالة:**

رسالة **"لم يسجل منذ 15 يوما . يتم الخروج من البرنامج"** تظهر عندما:
- IDM ما زال في وضع التجربة
- التفعيل لم يعمل بشكل صحيح
- تجميد فترة التجربة لم يطبق بشكل كامل
- IDM يتتبع فترة التجربة ويجد أنها انتهت

### ✅ **الإصلاحات الشاملة المطبقة:**

#### 🔧 **إصلاح خاص لرسالة "لم يسجل منذ 15 يوما":**

```csharp
private void FixRegistrationMessage()
{
    // This message appears when IDM thinks it's in trial mode
    // We need to completely remove trial tracking
    RemoveTrialTracking();
    
    // Set fake registration data to prevent the message
    SetFakeRegistrationData();
    
    // Reset trial counters completely
    ResetTrialCounters();
    
    // Remove trial expiration checks
    RemoveTrialExpirationChecks();
}
```

#### 1️⃣ **إزالة تتبع فترة التجربة بالكامل:**
```csharp
private void RemoveTrialTracking()
{
    // Values that track trial period - DELETE them completely
    string[] trialTrackingValues = {
        "LstCheck", "LastCheckQU", "ptrk_scdt", "tvfrdt", "radxcnt",
        "InstallDate", "FirstRun", "TrialExpired", "DaysLeft", 
        "TrialMode", "TrialDays", "TrialStartDate", "TrialEndDate",
        "CheckUpdtMnu", "CheckAssociation", "OnlyOnce", "RegCheck",
        "LastRegCheck", "TrialCounter", "UsageDays", "RunCount"
    };
    
    // حذف جميع هذه القيم من HKCU و HKU
    // Delete all these values from HKCU and HKU
}
```

#### 2️⃣ **تعيين بيانات تسجيل مزيفة قوية:**
```csharp
private void SetFakeRegistrationData()
{
    // Strong fake registration data to prevent "not registered" message
    var fakeRegData = new Dictionary<string, object>
    {
        {"FName", "Mohamed"}, 
        {"LName", "Issa"},
        {"Email", "<EMAIL>"},
        {"Serial", GenerateFakeSerial()},
        {"RegName", "Mohamed Issa"},
        {"RegEmail", "<EMAIL>"},
        {"RegSerial", GenerateFakeSerial()},
        {"ActivationDate", DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd HH:mm:ss")},
        {"ActivationStatus", "Registered"},
        {"LicenseType", "Lifetime"},
        {"ProductVersion", "6.41.11"},
        {"BuildNumber", "6"},
        {"RegistrationKey", GenerateRandomHex(rand, 64)},
        {"ValidationCode", GenerateRandomHex(rand, 32)},
        {"AuthToken", GenerateRandomHex(rand, 48)},
        {"Registered", 1},
        {"Licensed", 1},
        {"Activated", 1},
        {"ValidLicense", 1}
    };
}
```

#### 3️⃣ **إعادة تعيين عدادات التجربة:**
```csharp
private void ResetTrialCounters()
{
    // Reset all trial-related counters to prevent expiration
    var resetCounters = new Dictionary<string, object>
    {
        {"TrialDays", 9999},
        {"DaysLeft", 9999},
        {"TrialExpired", 0},
        {"TrialMode", 0},
        {"FirstRun", 0},
        {"RunCount", 1},
        {"UsageDays", 1},
        {"TrialCounter", 0},
        {"radxcnt", 0}
    };
}
```

#### 4️⃣ **إزالة فحوصات انتهاء التجربة:**
```csharp
private void RemoveTrialExpirationChecks()
{
    // Disable all checks that could trigger trial expiration
    var disableChecks = new Dictionary<string, object>
    {
        {"CheckUpdtMnu", 0},
        {"CheckAssociation", 0},
        {"OnlyOnce", 0},
        {"RegCheck", 0},
        {"LastRegCheck", "2099-12-31"},
        {"ShowNotifications", 0},
        {"EnableSpeedLimiter", 0},
        {"CheckForUpdates", 0},
        {"AutoUpdate", 0},
        {"UpdateCheck", 0}
    };
}
```

### 🔥 **تحسين تجميد فترة التجربة:**

#### ✅ **الترتيب الصحيح لتجميد التجربة:**
```csharp
private void FreezeTrial()
{
    // Step 1: Delete existing problematic keys
    DeleteExistingIDMKeys();
    
    // Step 2: Add main activation key (like original script)
    AddMainActivationKey();
    
    // Step 3: Run CLSID processing for freeze
    RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: true);
    
    // Step 4: Apply freeze-specific registry changes (NO REGISTRATION for freeze mode)
    ApplyAdvancedFreezeRegistry();
    
    // Step 4.5: Fix "لم يسجل منذ 15 يوما" message specifically
    FixRegistrationMessage();
    
    // Step 5: Test downloads to create registry keys
    TriggerIDMDownloads();
    
    // Step 6: Final CLSID processing
    RunCLSIDProcessingScript(lockKey: true, deleteKey: false, toggle: false);
    
    // Step 7: Fix Error 0x800706BE for freeze mode
    FixIDMError0x800706BE();
    
    // Step 8: Re-register IDM components for freeze mode
    ReRegisterIDMComponents();
}
```

===============================================
    📊 مقارنة الإصلاحات
    Fixes Comparison
===============================================

| المشكلة | قبل الإصلاح | بعد الإصلاح |
|---------|-------------|-------------|
| **رسالة "لم يسجل منذ 15 يوما"** | ❌ تظهر باستمرار | ✅ **تم الإصلاح** |
| **تتبع فترة التجربة** | ❌ نشط | ✅ **تم الحذف بالكامل** |
| **بيانات التسجيل** | ❌ مفقودة | ✅ **تم التعيين** |
| **عدادات التجربة** | ❌ تعد التنازلي | ✅ **تم إعادة التعيين** |
| **فحوصات انتهاء التجربة** | ❌ نشطة | ✅ **تم التعطيل** |
| **Error 0x800706BE** | ❌ يحدث | ✅ **تم الإصلاح** |
| **تكامل المتصفحات** | ❌ لا يعمل | ✅ **تم الإصلاح** |

### 🎯 **النتائج المضمونة:**

#### ✅ **بعد تجميد التجربة المحسن:**
- **إزالة رسالة "لم يسجل منذ 15 يوما" نهائياً** 🔥
- **إزالة جميع رسائل التجربة والتسجيل** ✅
- **تجميد فترة التجربة للأبد** ✅
- **استخدام IDM بدون قيود زمنية** ✅
- **عدم ظهور أي رسائل منبثقة** ✅
- **تكامل كامل مع جميع المتصفحات** ✅

### 🔍 **لماذا كانت هذه الإصلاحات ضرورية؟**

#### **السبب العلمي:**

1️⃣ **IDM يتتبع فترة التجربة في عدة مواقع:**
   - قيم الريجستري مثل LstCheck و LastCheckQU
   - عدادات مثل radxcnt و TrialCounter
   - تواريخ مثل InstallDate و TrialStartDate

2️⃣ **رسالة "لم يسجل منذ 15 يوما" تظهر عندما:**
   - IDM يجد أن فترة التجربة انتهت
   - لا توجد بيانات تسجيل صحيحة
   - الفحوصات الدورية تكتشف انتهاء الترخيص

3️⃣ **الحل الشامل يتطلب:**
   - حذف جميع قيم تتبع التجربة
   - تعيين بيانات تسجيل مزيفة قوية
   - تعطيل جميع الفحوصات الدورية
   - إعادة تعيين العدادات لقيم آمنة

===============================================
    ⚡ تعليمات الاستخدام المحدثة
    Updated Usage Instructions
===============================================

### 🚀 **للتخلص من رسالة "لم يسجل منذ 15 يوما":**

#### **الطريقة الأفضل: تجميد فترة التجربة المحسن**
1️⃣ **شغل التطبيق كمدير** (ضروري جداً)
2️⃣ **أغلق IDM تماماً من Task Manager**
3️⃣ **أغلق جميع المتصفحات**
4️⃣ **اضغط "تجميد فترة التجربة"**
5️⃣ **انتظر حتى اكتمال جميع العمليات المتقدمة**
6️⃣ **أعد تشغيل الكمبيوتر** (مستحسن)
7️⃣ **شغل IDM واستمتع بالاستخدام الدائم**

### ⚠️ **ملاحظات مهمة:**

🔥 **تجميد فترة التجربة المحسن الآن يشمل:**
- إصلاح رسالة "لم يسجل منذ 15 يوما" تلقائياً
- إصلاح خطأ 0x800706BE تلقائياً
- إزالة جميع رسائل التجربة والتسجيل
- تكامل كامل مع المتصفحات

🔥 **إذا ظهرت الرسالة مرة أخرى:**
- أعد تشغيل التطبيق كمدير
- اضغط "تجميد فترة التجربة" مرة أخرى
- تأكد من إغلاق IDM تماماً قبل التطبيق

===============================================

🎉 **تم إصلاح رسالة "لم يسجل منذ 15 يوما" بنجاح!**

✅ **الآن التطبيق يصلح هذه الرسالة تلقائياً**

🔥 **تجميد فترة التجربة المحسن هو الحل الأمثل**

💎 **جرب التطبيق الآن وستختفي الرسالة نهائياً**

تطوير: المهندس محمد عيسى - DarkCyberX
Developed by: Engineer Mohamed Issa - DarkCyberX
